package migration.db.crm

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil
import net.datatp.module.data.db.util.DBUtil

import javax.sql.DataSource
import java.sql.Connection

public class AlterTableSet extends DBMigrationRunnableSet {
    public AlterTableSet() {
        super("""Alter CRM Tables""");

    String label = """Alter CRM Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute("""
            ALTER TABLE public.lgc_sales_specific_service_inquiry ADD type_of_service varchar(255) NULL;
            ALTER TABLE public.lgc_sales_specific_service_inquiry ALTER COLUMN type_of_service SET STORAGE PLAIN;
          """);
          connUtil.execute("""
            UPDATE lgc_sales_specific_service_inquiry
            SET type_of_service = CASE 
                WHEN mode = 'SEA_FCL' AND purpose = 'EXPORT' THEN 'SeaExpTransactions_FCL' 
                WHEN mode = 'SEA_FCL' AND purpose = 'IMPORT' THEN 'SeaImpTransactions_FCL'  
                WHEN mode = 'SEA_LCL' AND purpose = 'EXPORT' THEN 'SeaExpTransactions_LCL'  
                WHEN mode = 'SEA_LCL' AND purpose = 'IMPORT' THEN 'SeaImpTransactions_LCL'  
                WHEN mode = 'AIR' AND purpose = 'EXPORT' THEN 'AirExpTransactions'  
                WHEN mode = 'AIR' AND purpose = 'IMPORT' THEN 'AirImpTransactions'  
                ELSE 'InlandTrucking' 
            END;
          """);
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;

DataSource crmDs = DBUtil.createPostgresDs('datatp-crm', 'datatp-crm', '**********************************************');
Connection conn = crmDs.getConnection();

DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableSet migration = new AlterTableSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"