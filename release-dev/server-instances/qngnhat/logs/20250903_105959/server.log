2025-09-03T11:00:00.232+07:00  INFO 1714 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 1714 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-03T11:00:00.233+07:00  INFO 1714 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-03T11:00:01.282+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.353+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 67 ms. Found 22 JPA repository interfaces.
2025-09-03T11:00:01.362+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.364+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T11:00:01.364+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.370+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-03T11:00:01.372+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.375+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-03T11:00:01.421+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.426+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-03T11:00:01.434+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.437+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-03T11:00:01.437+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.441+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-03T11:00:01.444+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.448+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-03T11:00:01.452+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.454+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T11:00:01.454+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.455+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:00:01.455+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.461+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-03T11:00:01.466+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.469+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T11:00:01.473+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.476+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T11:00:01.477+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.483+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-03T11:00:01.483+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.486+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-03T11:00:01.487+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.487+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:00:01.487+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.488+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-03T11:00:01.488+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.492+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T11:00:01.492+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.493+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-03T11:00:01.493+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.493+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:00:01.493+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.503+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-03T11:00:01.514+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.519+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-03T11:00:01.520+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.523+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-03T11:00:01.523+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.526+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T11:00:01.527+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.532+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-03T11:00:01.532+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.537+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-03T11:00:01.537+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.545+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T11:00:01.546+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.555+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-03T11:00:01.555+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.570+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-03T11:00:01.570+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.571+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T11:00:01.576+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.577+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:00:01.578+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.585+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T11:00:01.587+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.624+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 65 JPA repository interfaces.
2025-09-03T11:00:01.624+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.625+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T11:00:01.630+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:00:01.634+07:00  INFO 1714 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-03T11:00:01.855+07:00  INFO 1714 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-03T11:00:01.863+07:00  INFO 1714 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-03T11:00:02.182+07:00  WARN 1714 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-03T11:00:02.381+07:00  INFO 1714 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-03T11:00:02.383+07:00  INFO 1714 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-03T11:00:02.396+07:00  INFO 1714 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-03T11:00:02.396+07:00  INFO 1714 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1917 ms
2025-09-03T11:00:02.447+07:00  WARN 1714 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:00:02.447+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-03T11:00:02.555+07:00  INFO 1714 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@6354a50a
2025-09-03T11:00:02.556+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-03T11:00:02.561+07:00  WARN 1714 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:00:02.561+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T11:00:02.566+07:00  INFO 1714 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@34c31a34
2025-09-03T11:00:02.567+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T11:00:02.567+07:00  WARN 1714 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:00:02.567+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-03T11:00:02.575+07:00  INFO 1714 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2057d8ec
2025-09-03T11:00:02.575+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-03T11:00:02.576+07:00  WARN 1714 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:00:02.576+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-03T11:00:02.584+07:00  INFO 1714 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@76320633
2025-09-03T11:00:02.584+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-03T11:00:02.584+07:00  WARN 1714 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:00:02.584+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T11:00:02.592+07:00  INFO 1714 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@60bd11f7
2025-09-03T11:00:02.592+07:00  INFO 1714 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T11:00:02.592+07:00  INFO 1714 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-03T11:00:02.637+07:00  INFO 1714 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-03T11:00:02.640+07:00  INFO 1714 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@466fb9be{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10640786016441500930/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@25699129{STARTED}}
2025-09-03T11:00:02.640+07:00  INFO 1714 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@466fb9be{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10640786016441500930/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@25699129{STARTED}}
2025-09-03T11:00:02.641+07:00  INFO 1714 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@63a833c5{STARTING}[12.0.15,sto=0] @2974ms
2025-09-03T11:00:02.746+07:00  INFO 1714 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:00:02.774+07:00  INFO 1714 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-03T11:00:02.788+07:00  INFO 1714 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:00:02.919+07:00  INFO 1714 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:00:02.955+07:00  WARN 1714 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:00:03.571+07:00  INFO 1714 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:00:03.579+07:00  INFO 1714 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7c78d028] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:00:03.820+07:00  INFO 1714 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:00:04.046+07:00  INFO 1714 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-03T11:00:04.048+07:00  INFO 1714 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-03T11:00:04.056+07:00  INFO 1714 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:00:04.057+07:00  INFO 1714 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:00:04.083+07:00  INFO 1714 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:00:04.096+07:00  WARN 1714 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:00:06.117+07:00  INFO 1714 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:00:06.118+07:00  INFO 1714 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3a18af12] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:00:06.340+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T11:00:06.340+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T11:00:06.352+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T11:00:06.352+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T11:00:06.365+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T11:00:06.365+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-03T11:00:06.880+07:00  INFO 1714 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:00:06.889+07:00  INFO 1714 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:00:06.891+07:00  INFO 1714 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:00:06.912+07:00  INFO 1714 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:00:06.922+07:00  WARN 1714 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:00:07.385+07:00  INFO 1714 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:00:07.385+07:00  INFO 1714 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4180cfca] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:00:07.474+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T11:00:07.474+07:00  WARN 1714 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-03T11:00:07.799+07:00  INFO 1714 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:00:07.834+07:00  INFO 1714 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-03T11:00:07.838+07:00  INFO 1714 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-03T11:00:07.839+07:00  INFO 1714 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:00:07.846+07:00  WARN 1714 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:00:07.991+07:00  INFO 1714 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-03T11:00:08.534+07:00  INFO 1714 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T11:00:08.537+07:00  INFO 1714 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T11:00:08.575+07:00  INFO 1714 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-03T11:00:08.620+07:00  INFO 1714 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-03T11:00:08.688+07:00  INFO 1714 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-03T11:00:08.719+07:00  INFO 1714 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T11:00:08.751+07:00  INFO 1714 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1055184ms : this is harmless.
2025-09-03T11:00:08.762+07:00  INFO 1714 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-03T11:00:08.766+07:00  INFO 1714 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T11:00:08.779+07:00  INFO 1714 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 8178747ms : this is harmless.
2025-09-03T11:00:08.780+07:00  INFO 1714 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-03T11:00:08.792+07:00  INFO 1714 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-03T11:00:08.793+07:00  INFO 1714 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-03T11:00:09.829+07:00  INFO 1714 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-03T11:00:09.829+07:00  INFO 1714 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:00:09.830+07:00  WARN 1714 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:00:10.493+07:00  INFO 1714 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@11:00:00+0700 to 03/09/2025@11:15:00+0700
2025-09-03T11:00:10.493+07:00  INFO 1714 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@11:00:00+0700 to 03/09/2025@11:15:00+0700
2025-09-03T11:00:11.634+07:00  INFO 1714 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-03T11:00:11.634+07:00  INFO 1714 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:00:11.635+07:00  WARN 1714 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:00:11.899+07:00  INFO 1714 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-03T11:00:11.899+07:00  INFO 1714 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-03T11:00:11.899+07:00  INFO 1714 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-03T11:00:11.899+07:00  INFO 1714 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-03T11:00:11.899+07:00  INFO 1714 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-03T11:00:13.435+07:00  WARN 1714 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 343ac9c5-bf42-4461-b94b-61c6c7f372b9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-03T11:00:13.439+07:00  INFO 1714 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-03T11:00:13.753+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T11:00:13.753+07:00  INFO 1714 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-03T11:00:13.754+07:00  INFO 1714 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-03T11:00:13.757+07:00  INFO 1714 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T11:00:13.757+07:00  INFO 1714 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T11:00:13.757+07:00  INFO 1714 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T11:00:13.818+07:00  INFO 1714 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03T11:00:13.818+07:00  INFO 1714 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-03T11:00:13.820+07:00  INFO 1714 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-03T11:00:13.828+07:00  INFO 1714 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@8900a7f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T11:00:13.829+07:00  INFO 1714 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-03T11:00:13.830+07:00  INFO 1714 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-03T11:00:13.872+07:00  INFO 1714 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-03T11:00:13.872+07:00  INFO 1714 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-03T11:00:13.878+07:00  INFO 1714 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.034 seconds (process running for 14.211)
2025-09-03T11:00:16.119+07:00  INFO 1714 --- [qtp486903996-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01t5ig1f0jg9dupt2c4th39bjb0
2025-09-03T11:00:16.405+07:00  INFO 1714 --- [qtp486903996-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01t5ig1f0jg9dupt2c4th39bjb0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:00:16.771+07:00  INFO 1714 --- [qtp486903996-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:01:03.259+07:00  INFO 1714 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:01:07.544+07:00  INFO 1714 --- [qtp486903996-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01t5ig1f0jg9dupt2c4th39bjb0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:01:07.544+07:00  INFO 1714 --- [qtp486903996-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01t5ig1f0jg9dupt2c4th39bjb0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:01:07.567+07:00  INFO 1714 --- [qtp486903996-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:01:07.567+07:00  INFO 1714 --- [qtp486903996-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:01:17.461+07:00  INFO 1714 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-03T11:01:17.492+07:00  INFO 1714 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:01:57.458+07:00  INFO 1714 --- [qtp486903996-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01t5ig1f0jg9dupt2c4th39bjb0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:01:57.461+07:00  INFO 1714 --- [qtp486903996-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01t5ig1f0jg9dupt2c4th39bjb0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:01:57.475+07:00  INFO 1714 --- [qtp486903996-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:01:57.481+07:00  INFO 1714 --- [qtp486903996-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:02:06.670+07:00  INFO 1714 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:03:02.770+07:00  INFO 1714 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:03:16.849+07:00  INFO 1714 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-03T11:03:16.869+07:00  INFO 1714 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:03:23.570+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@8900a7f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T11:03:23.571+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T11:03:23.571+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T11:03:23.571+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-03T11:03:23.572+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-03T11:03:23.608+07:00  INFO 1714 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:03:23.763+07:00  INFO 1714 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-03T11:03:23.769+07:00  INFO 1714 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-03T11:03:23.795+07:00  INFO 1714 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:23.796+07:00  INFO 1714 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:23.797+07:00  INFO 1714 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:23.797+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T11:03:23.798+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T11:03:23.798+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-03T11:03:23.799+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-03T11:03:23.799+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-03T11:03:23.799+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-03T11:03:23.800+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T11:03:23.801+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T11:03:23.802+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-03T11:03:23.802+07:00  INFO 1714 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-03T11:03:23.805+07:00  INFO 1714 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@63a833c5{STOPPING}[12.0.15,sto=0]
2025-09-03T11:03:23.807+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-03T11:03:23.808+07:00  INFO 1714 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@466fb9be{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10640786016441500930/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@25699129{STOPPED}}
