2025-09-03T14:33:22.215+07:00  INFO 25028 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 25028 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-03T14:33:22.215+07:00  INFO 25028 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-03T14:33:22.938+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.003+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-03T14:33:23.011+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.013+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T14:33:23.013+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.021+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-03T14:33:23.022+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.025+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T14:33:23.069+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.075+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-03T14:33:23.083+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.085+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-03T14:33:23.086+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.089+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T14:33:23.092+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.097+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-03T14:33:23.101+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.104+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T14:33:23.104+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.104+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T14:33:23.104+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.111+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-03T14:33:23.116+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.118+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T14:33:23.121+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.125+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T14:33:23.125+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.132+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T14:33:23.133+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.136+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-03T14:33:23.136+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.137+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T14:33:23.137+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.137+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-03T14:33:23.138+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.142+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T14:33:23.142+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.143+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-03T14:33:23.143+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.143+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T14:33:23.143+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.153+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-03T14:33:23.162+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.169+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-03T14:33:23.169+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.172+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-03T14:33:23.172+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.176+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T14:33:23.176+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.181+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-03T14:33:23.182+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.186+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T14:33:23.186+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.193+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T14:33:23.194+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.203+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-03T14:33:23.204+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.218+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-03T14:33:23.218+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.219+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-03T14:33:23.224+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.224+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T14:33:23.224+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.232+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T14:33:23.233+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.269+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 65 JPA repository interfaces.
2025-09-03T14:33:23.269+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.270+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T14:33:23.274+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T14:33:23.277+07:00  INFO 25028 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-03T14:33:23.488+07:00  INFO 25028 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-03T14:33:23.492+07:00  INFO 25028 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-03T14:33:23.768+07:00  WARN 25028 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-03T14:33:23.955+07:00  INFO 25028 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-03T14:33:23.957+07:00  INFO 25028 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-03T14:33:23.968+07:00  INFO 25028 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-03T14:33:23.969+07:00  INFO 25028 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1622 ms
2025-09-03T14:33:24.021+07:00  WARN 25028 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T14:33:24.021+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-03T14:33:24.118+07:00  INFO 25028 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1c1bc904
2025-09-03T14:33:24.119+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-03T14:33:24.123+07:00  WARN 25028 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T14:33:24.123+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T14:33:24.132+07:00  INFO 25028 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4f9e32f2
2025-09-03T14:33:24.132+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T14:33:24.132+07:00  WARN 25028 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T14:33:24.132+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-03T14:33:24.149+07:00  INFO 25028 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1fd1deed
2025-09-03T14:33:24.149+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-03T14:33:24.149+07:00  WARN 25028 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T14:33:24.149+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-03T14:33:24.253+07:00  INFO 25028 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2b01bb54
2025-09-03T14:33:24.254+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-03T14:33:24.254+07:00  WARN 25028 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T14:33:24.254+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T14:33:24.296+07:00  INFO 25028 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@20c1cb0c
2025-09-03T14:33:24.296+07:00  INFO 25028 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T14:33:24.296+07:00  INFO 25028 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-03T14:33:24.341+07:00  INFO 25028 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-03T14:33:24.343+07:00  INFO 25028 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@31efe094{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12060754076765750613/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6987a0f3{STARTED}}
2025-09-03T14:33:24.343+07:00  INFO 25028 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@31efe094{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12060754076765750613/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6987a0f3{STARTED}}
2025-09-03T14:33:24.344+07:00  INFO 25028 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@74c919b2{STARTING}[12.0.15,sto=0] @2673ms
2025-09-03T14:33:24.455+07:00  INFO 25028 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T14:33:24.486+07:00  INFO 25028 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-03T14:33:24.502+07:00  INFO 25028 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T14:33:24.627+07:00  INFO 25028 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T14:33:24.663+07:00  WARN 25028 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T14:33:25.271+07:00  INFO 25028 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T14:33:25.280+07:00  INFO 25028 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@44572f11] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T14:33:25.430+07:00  INFO 25028 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:25.639+07:00  INFO 25028 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-03T14:33:25.641+07:00  INFO 25028 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-03T14:33:25.647+07:00  INFO 25028 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T14:33:25.649+07:00  INFO 25028 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T14:33:25.674+07:00  INFO 25028 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T14:33:25.687+07:00  WARN 25028 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T14:33:27.845+07:00  INFO 25028 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T14:33:27.846+07:00  INFO 25028 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@369436c8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T14:33:28.422+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T14:33:28.422+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T14:33:28.436+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T14:33:28.436+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T14:33:28.451+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T14:33:28.451+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-03T14:33:29.037+07:00  INFO 25028 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:29.044+07:00  INFO 25028 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T14:33:29.046+07:00  INFO 25028 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T14:33:29.067+07:00  INFO 25028 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T14:33:29.071+07:00  WARN 25028 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T14:33:29.603+07:00  INFO 25028 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T14:33:29.604+07:00  INFO 25028 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@437968b7] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T14:33:29.687+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T14:33:29.688+07:00  WARN 25028 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-03T14:33:30.321+07:00  INFO 25028 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:30.358+07:00  INFO 25028 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-03T14:33:30.362+07:00  INFO 25028 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-03T14:33:30.363+07:00  INFO 25028 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:33:30.370+07:00  WARN 25028 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T14:33:30.512+07:00  INFO 25028 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-03T14:33:31.118+07:00  INFO 25028 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T14:33:31.121+07:00  INFO 25028 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T14:33:31.169+07:00  INFO 25028 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-03T14:33:31.230+07:00  INFO 25028 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-03T14:33:31.362+07:00  INFO 25028 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-03T14:33:31.394+07:00  INFO 25028 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T14:33:31.424+07:00  INFO 25028 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 13853917ms : this is harmless.
2025-09-03T14:33:31.434+07:00  INFO 25028 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-03T14:33:31.437+07:00  INFO 25028 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T14:33:31.450+07:00  INFO 25028 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 20977481ms : this is harmless.
2025-09-03T14:33:31.452+07:00  INFO 25028 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-03T14:33:31.465+07:00  INFO 25028 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-03T14:33:31.466+07:00  INFO 25028 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-03T14:33:32.552+07:00  INFO 25028 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-03T14:33:32.552+07:00  INFO 25028 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:33:32.553+07:00  WARN 25028 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T14:33:33.556+07:00  INFO 25028 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@14:30:00+0700 to 03/09/2025@14:45:00+0700
2025-09-03T14:33:33.556+07:00  INFO 25028 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@14:30:00+0700 to 03/09/2025@14:45:00+0700
2025-09-03T14:33:34.927+07:00  INFO 25028 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-03T14:33:34.927+07:00  INFO 25028 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:33:34.928+07:00  WARN 25028 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T14:33:35.251+07:00  INFO 25028 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-03T14:33:35.251+07:00  INFO 25028 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-03T14:33:35.251+07:00  INFO 25028 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-03T14:33:35.251+07:00  INFO 25028 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-03T14:33:35.251+07:00  INFO 25028 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-03T14:33:37.384+07:00  WARN 25028 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b6917841-6cd6-4571-aee5-83657818cfbf

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-03T14:33:37.398+07:00  INFO 25028 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-03T14:33:37.961+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T14:33:37.961+07:00  INFO 25028 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-03T14:33:37.962+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-03T14:33:37.962+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T14:33:37.962+07:00  INFO 25028 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-03T14:33:37.963+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T14:33:37.963+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T14:33:37.964+07:00  INFO 25028 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-03T14:33:37.964+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T14:33:37.964+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T14:33:37.964+07:00  INFO 25028 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-03T14:33:37.964+07:00  INFO 25028 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-03T14:33:38.000+07:00  INFO 25028 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T14:33:38.008+07:00  INFO 25028 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T14:33:38.008+07:00  INFO 25028 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T14:33:38.116+07:00  INFO 25028 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03T14:33:38.116+07:00  INFO 25028 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-03T14:33:38.118+07:00  INFO 25028 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-03T14:33:38.128+07:00  INFO 25028 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@77fe8c72{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T14:33:38.129+07:00  INFO 25028 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-03T14:33:38.130+07:00  INFO 25028 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-03T14:33:38.161+07:00  INFO 25028 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-03T14:33:38.161+07:00  INFO 25028 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-03T14:33:38.172+07:00  INFO 25028 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 16.267 seconds (process running for 16.5)
2025-09-03T14:33:38.299+07:00  INFO 25028 --- [qtp391734039-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0b8wp3yf015ouxurglu15030w1
2025-09-03T14:33:38.299+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0qrpcqdj2qav61cye5gdmeb4lh0
2025-09-03T14:33:38.530+07:00  INFO 25028 --- [qtp391734039-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0qrpcqdj2qav61cye5gdmeb4lh0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:33:38.531+07:00  INFO 25028 --- [qtp391734039-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:33:38.947+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:33:38.956+07:00  INFO 25028 --- [qtp391734039-62] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:34:06.084+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:34:41.225+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-03T14:34:41.251+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:35:02.281+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:35:02.285+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:36:05.387+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:36:40.488+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-03T14:36:40.527+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:36:57.139+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:36:57.149+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:36:57.154+07:00  INFO 25028 --- [qtp391734039-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:36:57.171+07:00  INFO 25028 --- [qtp391734039-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:03.394+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:03.430+07:00  INFO 25028 --- [qtp391734039-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:03.505+07:00  INFO 25028 --- [qtp391734039-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:03.505+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:06.573+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:37:10.567+07:00  INFO 25028 --- [qtp391734039-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:10.578+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:10.593+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:10.625+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:16.714+07:00  INFO 25028 --- [qtp391734039-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:16.729+07:00  INFO 25028 --- [qtp391734039-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:16.742+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:16.747+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:24.521+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:24.522+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:24.527+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:24.527+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:35.008+07:00  INFO 25028 --- [qtp391734039-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:35.010+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:37:35.021+07:00  INFO 25028 --- [qtp391734039-67] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:37:35.026+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:38:04.673+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:38:44.834+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-03T14:38:44.848+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:39:06.897+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:40:03.994+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:40:03.999+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:40:17.584+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:17.584+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:17.684+07:00  INFO 25028 --- [qtp391734039-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 15 records
2025-09-03T14:40:17.684+07:00  INFO 25028 --- [qtp391734039-39] c.d.f.s.report.PerformanceReportLogic    : Retrieved 15 records
2025-09-03T14:40:17.699+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:17.699+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:17.851+07:00  INFO 25028 --- [qtp391734039-66] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-03T14:40:17.851+07:00  INFO 25028 --- [qtp391734039-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-03T14:40:20.452+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:20.452+07:00  INFO 25028 --- [qtp391734039-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:20.946+07:00  INFO 25028 --- [qtp391734039-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:20.946+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:23.822+07:00  INFO 25028 --- [qtp391734039-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:23.823+07:00  INFO 25028 --- [qtp391734039-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:29.930+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:29.931+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:40:45.115+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-03T14:40:45.134+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:40:52.198+07:00  INFO 25028 --- [qtp391734039-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:40:52.199+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:40:52.215+07:00  INFO 25028 --- [qtp391734039-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:40:52.215+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:06.167+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:41:25.483+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:25.494+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:25.510+07:00  INFO 25028 --- [qtp391734039-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:25.517+07:00  INFO 25028 --- [qtp391734039-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:43.793+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:43.794+07:00  INFO 25028 --- [qtp391734039-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:43.805+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:43.805+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:48.310+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:48.311+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:48.318+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:48.318+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:53.091+07:00  INFO 25028 --- [qtp391734039-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:53.100+07:00  INFO 25028 --- [qtp391734039-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:53.161+07:00  INFO 25028 --- [qtp391734039-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:53.166+07:00  INFO 25028 --- [qtp391734039-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:59.883+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:59.884+07:00  INFO 25028 --- [qtp391734039-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:41:59.891+07:00  INFO 25028 --- [qtp391734039-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:41:59.891+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:42:03.479+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:42:13.440+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:42:13.440+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:42:44.571+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-03T14:42:44.586+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:43:06.622+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:43:24.335+07:00  INFO 25028 --- [Scheduler-449680686-1] n.d.m.session.AppHttpSessionListener     : The session node0qrpcqdj2qav61cye5gdmeb4lh0 is destroyed.
2025-09-03T14:43:29.124+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-03T14:44:00.943+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-03T14:44:02.711+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:44:12.353+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:44:12.355+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:44:12.367+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:44:12.367+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:44:15.505+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:15.505+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:15.545+07:00  INFO 25028 --- [qtp391734039-66] c.d.f.s.report.PerformanceReportLogic    : Retrieved 15 records
2025-09-03T14:44:15.545+07:00  INFO 25028 --- [qtp391734039-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 15 records
2025-09-03T14:44:15.552+07:00  INFO 25028 --- [qtp391734039-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:15.553+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:15.642+07:00  INFO 25028 --- [qtp391734039-35] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-03T14:44:15.643+07:00  INFO 25028 --- [qtp391734039-36] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-03T14:44:17.182+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:17.184+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:44:44.819+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-03T14:44:44.832+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:45:05.872+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:45:05.888+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T14:45:05.897+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@14:45:05+0700
2025-09-03T14:45:05.923+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@14:45:00+0700 to 03/09/2025@15:00:00+0700
2025-09-03T14:45:05.923+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@14:45:00+0700 to 03/09/2025@15:00:00+0700
2025-09-03T14:45:05.923+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:46:02.028+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:46:03.986+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:03.999+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:04.035+07:00  INFO 25028 --- [qtp391734039-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:04.043+07:00  INFO 25028 --- [qtp391734039-70] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:12.649+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:12.657+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:12.808+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:12.820+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:15.116+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:15.125+07:00  INFO 25028 --- [qtp391734039-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:46:15.131+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:15.135+07:00  INFO 25028 --- [qtp391734039-70] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:46:44.137+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-09-03T14:46:44.142+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:46:52.097+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:46:52.097+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:47:05.175+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:47:21.179+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:47:21.179+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:47:47.177+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:47:47.188+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:47:47.204+07:00  INFO 25028 --- [qtp391734039-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:47:47.214+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:47:52.860+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:47:52.864+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:47:52.890+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:47:52.915+07:00 ERROR 25028 --- [qtp391734039-35] n.d.m.monitor.call.EndpointCallContext   : Start call with component SaleTaskReportService, method salemanSystemPerformanceReport, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0b8wp3yf015ouxurglu15030w1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : { },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "rangeFilters" : [ {
    "name" : "reportDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/09/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 100000
} ]
2025-09-03T14:47:52.915+07:00 ERROR 25028 --- [qtp391734039-64] n.d.m.monitor.call.EndpointCallContext   : Start call with component SaleTaskReportService, method salemanSystemPerformanceReport, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0b8wp3yf015ouxurglu15030w1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : { },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "rangeFilters" : [ {
    "name" : "reportDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/09/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 100000
} ]
2025-09-03T14:47:52.916+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:47:52.917+07:00 ERROR 25028 --- [qtp391734039-35] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportLogic.salemanSystemPerformanceReport(SaleTaskReportLogic.java:383)
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportService.salemanSystemPerformanceReport(SaleTaskReportService.java:72)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportService$$SpringCGLIB$$0.salemanSystemPerformanceReport(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T14:47:52.917+07:00 ERROR 25028 --- [qtp391734039-64] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportLogic.salemanSystemPerformanceReport(SaleTaskReportLogic.java:383)
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportService.salemanSystemPerformanceReport(SaleTaskReportService.java:72)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.report.SaleTaskReportService$$SpringCGLIB$$0.salemanSystemPerformanceReport(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T14:47:52.925+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T14:47:52.925+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T14:47:55.705+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:47:55.705+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:48:06.317+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:48:07.328+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:48:07.329+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:48:07.339+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:48:07.339+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:48:11.323+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:48:11.331+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:48:11.369+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:48:11.387+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:48:36.261+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:48:36.262+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:48:43.428+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-03T14:48:43.445+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:49:04.490+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:49:33.069+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:49:33.070+07:00  INFO 25028 --- [qtp391734039-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:49:33.097+07:00  INFO 25028 --- [qtp391734039-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:49:33.097+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:49:44.708+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:49:44.708+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:50:06.649+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:50:06.654+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:50:23.943+07:00  INFO 25028 --- [qtp391734039-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:23.944+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:23.962+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:23.962+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:27.911+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:27.969+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:27.985+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:27.992+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:31.957+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:50:31.957+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:50:42.745+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 4
2025-09-03T14:50:42.763+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:50:49.667+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:49.668+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:50:49.677+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:49.677+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:50:54.448+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:50:54.448+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:51:03.802+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:51:16.850+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:51:16.851+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:51:16.858+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:51:16.858+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:51:20.250+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:51:20.252+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:51:55.299+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:51:55.302+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:51:55.323+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:51:55.323+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:51:59.102+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:51:59.102+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:52:06.954+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:52:23.194+07:00  INFO 25028 --- [qtp391734039-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:52:23.195+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:52:23.212+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:52:23.212+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:52:28.072+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:52:28.074+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:52:42.059+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-03T14:52:42.084+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:52:50.304+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:52:50.305+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:52:50.313+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:52:50.313+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:52:58.045+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:52:58.045+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:53:03.119+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:53:22.416+07:00  INFO 25028 --- [qtp391734039-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:53:22.417+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:53:22.427+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:53:22.427+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:53:26.129+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:53:26.134+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:53:36.361+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:53:36.366+07:00  INFO 25028 --- [qtp391734039-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:54:06.254+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:54:41.394+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 2
2025-09-03T14:54:41.421+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:55:02.459+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:55:02.464+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:55:06.724+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:06.725+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:06.736+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:06.736+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:15.929+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:15.931+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:15.936+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:15.937+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:19.638+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:19.638+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:19.648+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:19.648+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:24.105+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:24.106+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:24.122+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:24.123+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:41.758+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:55:41.766+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:55:57.776+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:57.777+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:55:57.786+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:55:57.786+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:05.586+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:56:07.872+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:07.872+07:00  INFO 25028 --- [qtp391734039-100] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:15.300+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:15.301+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:22.340+07:00  INFO 25028 --- [qtp391734039-100] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:22.340+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:56:34.573+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:34.587+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:34.607+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:34.623+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:40.709+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-03T14:56:40.721+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:56:48.744+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:48.745+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:48.761+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:48.764+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:57.748+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:57.748+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:56:57.758+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:56:57.758+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:57:06.760+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:57:18.465+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:57:18.465+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:58:04.893+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:58:17.185+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:17.186+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:17.199+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:17.199+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:21.761+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:21.762+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:21.787+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:21.787+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:24.757+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:58:24.759+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:58:33.278+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:33.279+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:58:33.286+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:33.286+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:58:37.303+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:58:37.303+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:58:45.006+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 4
2025-09-03T14:58:45.037+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:59:03.676+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:03.680+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:03.700+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:59:03.700+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:59:06.075+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:59:11.871+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:11.900+07:00  INFO 25028 --- [qtp391734039-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:11.901+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:59:11.904+07:00  INFO 25028 --- [qtp391734039-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:59:15.969+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:15.969+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:17.082+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:17.127+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:17.328+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:17.328+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:59:40.015+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:40.017+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:59:40.030+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:59:40.030+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:00:04.171+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:00:04.175+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-03T15:00:04.175+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-03T15:00:04.176+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-03T15:00:04.178+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:00:04.180+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@15:00:04+0700
2025-09-03T15:00:04.205+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@15:00:00+0700 to 03/09/2025@15:15:00+0700
2025-09-03T15:00:04.205+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@15:00:00+0700 to 03/09/2025@15:15:00+0700
2025-09-03T15:00:04.205+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T15:00:04.586+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:00:04.586+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:00:44.289+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T15:00:44.302+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:01:03.016+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:03.016+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:03.024+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:03.024+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:03.132+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:03.133+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:03.135+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:03.136+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:06.344+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:01:07.161+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:01:07.181+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:01:30.291+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:30.294+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:30.296+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:30.309+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:31.133+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:31.133+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:31.139+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:31.139+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:39.174+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:01:39.174+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:01:58.341+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:58.350+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:58.351+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:58.368+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:59.133+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:59.134+07:00  INFO 25028 --- [qtp391734039-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:01:59.140+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:01:59.141+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:02.174+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:02.174+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:03.432+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:02:07.303+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:07.303+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:25.230+07:00  INFO 25028 --- [qtp391734039-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:25.230+07:00  INFO 25028 --- [qtp391734039-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:25.234+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:25.234+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:29.781+07:00  INFO 25028 --- [qtp391734039-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:29.781+07:00  INFO 25028 --- [qtp391734039-115] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:39.300+07:00  INFO 25028 --- [qtp391734039-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:39.302+07:00  INFO 25028 --- [qtp391734039-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:39.306+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:39.306+07:00  INFO 25028 --- [qtp391734039-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:43.959+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:43.971+07:00  INFO 25028 --- [qtp391734039-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:02:43.982+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:44.015+07:00  INFO 25028 --- [qtp391734039-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:02:44.519+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-03T15:02:44.534+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:02:51.804+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:51.804+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:52.404+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:52.404+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:56.493+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:02:56.493+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:03:04.914+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:03:04.914+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:03:06.571+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:04:02.659+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:04:44.785+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-03T15:04:44.793+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:05:05.814+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:05:05.815+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:06:06.889+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:06:43.946+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-03T15:06:43.970+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:07:05.000+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:08:06.083+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:08:43.161+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:08:43.169+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:09:04.199+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:10:06.288+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:10:06.289+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:10:42.356+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-03T15:10:42.358+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:11:03.389+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:12:06.490+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:12:39.525+07:00  INFO 25028 --- [qtp391734039-99] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:12:39.525+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:12:41.559+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 3
2025-09-03T15:12:41.572+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:12:42.207+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:12:42.211+07:00  INFO 25028 --- [qtp391734039-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T15:13:02.600+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:14:05.715+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:14:40.799+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 7
2025-09-03T15:14:40.803+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:15:05.189+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:15:05.206+07:00  INFO 25028 --- [qtp391734039-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:15:05.455+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:15:05.455+07:00  INFO 25028 --- [qtp391734039-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:15:06.839+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:15:06.840+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:15:06.840+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@15:15:06+0700
2025-09-03T15:15:06.857+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@15:15:00+0700 to 03/09/2025@15:30:00+0700
2025-09-03T15:15:06.857+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@15:15:00+0700 to 03/09/2025@15:30:00+0700
2025-09-03T15:15:06.858+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T15:15:22.032+07:00  INFO 25028 --- [qtp391734039-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:15:22.035+07:00  INFO 25028 --- [qtp391734039-95] n.d.module.session.ClientSessionManager  : Add a client session id = node0b8wp3yf015ouxurglu15030w1, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T15:15:22.059+07:00  INFO 25028 --- [qtp391734039-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:15:22.059+07:00  INFO 25028 --- [qtp391734039-95] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T15:16:04.957+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:16:45.054+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-03T15:16:45.080+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:17:06.114+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:18:04.222+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:18:44.311+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:18:44.323+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:19:06.356+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:20:03.452+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:20:03.458+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:20:44.567+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:20:44.590+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:21:06.629+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:22:02.705+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:22:44.822+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-09-03T15:22:44.841+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:23:05.874+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:24:06.983+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:24:44.043+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:24:44.048+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:25:05.086+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:25:05.090+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:26:06.189+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:26:43.271+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-03T15:26:43.296+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:27:04.326+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:28:06.429+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:28:42.484+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:28:42.492+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:29:03.534+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:30:06.643+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:30:06.645+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:30:06.645+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T15:30:06.645+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@15:30:06+0700
2025-09-03T15:30:06.660+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@15:30:00+0700 to 03/09/2025@15:45:00+0700
2025-09-03T15:30:06.660+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@15:30:00+0700 to 03/09/2025@15:45:00+0700
2025-09-03T15:30:41.734+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T15:30:41.741+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:31:02.778+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:32:05.872+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:32:40.925+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:32:40.931+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:33:06.965+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:34:05.062+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:34:45.132+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T15:34:45.144+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:35:06.177+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:35:06.181+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:36:04.266+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:36:44.335+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:36:44.346+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:37:06.388+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:38:03.459+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:38:44.538+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:38:44.546+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:39:06.584+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:40:02.666+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:40:02.669+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:40:44.758+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T15:40:44.771+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:41:05.813+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:42:06.918+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:42:43.995+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:42:43.999+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:43:05.037+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:44:06.138+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:44:43.235+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T15:44:43.246+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:45:04.279+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:45:04.283+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:45:04.283+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T15:45:04.284+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@15:45:04+0700
2025-09-03T15:45:04.299+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@15:45:00+0700 to 03/09/2025@16:00:00+0700
2025-09-03T15:45:04.299+07:00  INFO 25028 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@15:45:00+0700 to 03/09/2025@16:00:00+0700
2025-09-03T15:46:06.405+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:46:42.498+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:46:42.514+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:47:03.545+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:48:06.651+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:48:41.734+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:48:41.743+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:49:02.781+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:50:05.894+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:50:05.900+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T15:50:40.950+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:50:40.956+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:51:06.994+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:52:05.093+07:00  INFO 25028 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T15:52:45.171+07:00  INFO 25028 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T15:52:45.185+07:00  INFO 25028 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:52:47.115+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@77fe8c72{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T15:52:47.116+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T15:52:47.116+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T15:52:47.116+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T15:52:47.116+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-03T15:52:47.117+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-03T15:52:47.131+07:00  INFO 25028 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T15:52:47.182+07:00  INFO 25028 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-03T15:52:47.187+07:00  INFO 25028 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-03T15:52:47.239+07:00  INFO 25028 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T15:52:47.242+07:00  INFO 25028 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T15:52:47.243+07:00  INFO 25028 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T15:52:47.244+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T15:52:47.245+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T15:52:47.245+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-03T15:52:47.245+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-03T15:52:47.246+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-03T15:52:47.246+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-03T15:52:47.246+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T15:52:47.246+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T15:52:47.246+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-03T15:52:47.247+07:00  INFO 25028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-03T15:52:47.250+07:00  INFO 25028 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@74c919b2{STOPPING}[12.0.15,sto=0]
2025-09-03T15:52:47.256+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-03T15:52:47.259+07:00  INFO 25028 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@31efe094{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12060754076765750613/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6987a0f3{STOPPED}}
