2025-09-03T11:03:34.507+07:00  INFO 2554 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 2554 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-03T11:03:34.508+07:00  INFO 2554 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-03T11:03:35.246+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.309+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-03T11:03:35.317+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.318+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T11:03:35.319+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.325+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-03T11:03:35.326+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.329+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T11:03:35.376+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.381+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-03T11:03:35.389+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.391+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-03T11:03:35.392+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.396+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-03T11:03:35.399+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.403+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-03T11:03:35.407+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.409+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T11:03:35.409+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.410+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:03:35.410+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.416+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-03T11:03:35.420+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.422+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-03T11:03:35.426+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.429+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T11:03:35.429+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.436+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-03T11:03:35.437+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.440+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-03T11:03:35.440+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.440+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:03:35.440+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.441+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-03T11:03:35.441+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.445+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T11:03:35.445+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.446+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-03T11:03:35.446+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.447+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:03:35.447+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.456+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-03T11:03:35.465+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.471+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-03T11:03:35.471+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.474+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-03T11:03:35.474+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.478+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-03T11:03:35.478+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.483+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-03T11:03:35.483+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.487+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-03T11:03:35.487+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.495+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-03T11:03:35.495+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.503+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-03T11:03:35.504+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.516+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 24 JPA repository interfaces.
2025-09-03T11:03:35.517+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.518+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-03T11:03:35.523+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.524+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-03T11:03:35.524+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.531+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-03T11:03:35.533+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.566+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 65 JPA repository interfaces.
2025-09-03T11:03:35.567+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.568+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-03T11:03:35.572+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-03T11:03:35.574+07:00  INFO 2554 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-03T11:03:35.772+07:00  INFO 2554 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-03T11:03:35.776+07:00  INFO 2554 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-03T11:03:36.044+07:00  WARN 2554 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-03T11:03:36.234+07:00  INFO 2554 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-03T11:03:36.236+07:00  INFO 2554 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-03T11:03:36.247+07:00  INFO 2554 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-03T11:03:36.247+07:00  INFO 2554 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1579 ms
2025-09-03T11:03:36.299+07:00  WARN 2554 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:03:36.299+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-03T11:03:36.402+07:00  INFO 2554 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2b53d6fc
2025-09-03T11:03:36.402+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-03T11:03:36.407+07:00  WARN 2554 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:03:36.407+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T11:03:36.414+07:00  INFO 2554 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@814b60b
2025-09-03T11:03:36.414+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T11:03:36.415+07:00  WARN 2554 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:03:36.415+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-03T11:03:36.423+07:00  INFO 2554 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@e822394
2025-09-03T11:03:36.423+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-03T11:03:36.423+07:00  WARN 2554 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:03:36.423+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-03T11:03:36.432+07:00  INFO 2554 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@76e56b17
2025-09-03T11:03:36.432+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-03T11:03:36.432+07:00  WARN 2554 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-03T11:03:36.432+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-03T11:03:36.440+07:00  INFO 2554 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2a7b4b19
2025-09-03T11:03:36.441+07:00  INFO 2554 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-03T11:03:36.441+07:00  INFO 2554 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-03T11:03:36.485+07:00  INFO 2554 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-03T11:03:36.487+07:00  INFO 2554 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@1d280bab{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16765833008127519605/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@12639753{STARTED}}
2025-09-03T11:03:36.487+07:00  INFO 2554 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@1d280bab{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16765833008127519605/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@12639753{STARTED}}
2025-09-03T11:03:36.489+07:00  INFO 2554 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@73c21b13{STARTING}[12.0.15,sto=0] @2565ms
2025-09-03T11:03:36.594+07:00  INFO 2554 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:03:36.621+07:00  INFO 2554 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-03T11:03:36.635+07:00  INFO 2554 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:03:36.756+07:00  INFO 2554 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:03:36.879+07:00  WARN 2554 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:03:37.516+07:00  INFO 2554 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:03:37.524+07:00  INFO 2554 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1fd4df9a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:03:37.698+07:00  INFO 2554 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:37.977+07:00  INFO 2554 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-03T11:03:37.981+07:00  INFO 2554 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-03T11:03:37.989+07:00  INFO 2554 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:03:37.991+07:00  INFO 2554 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:03:38.015+07:00  INFO 2554 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:03:38.067+07:00  WARN 2554 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:03:40.219+07:00  INFO 2554 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:03:40.220+07:00  INFO 2554 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2093b11b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:03:40.464+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T11:03:40.464+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T11:03:40.475+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-03T11:03:40.475+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-03T11:03:40.488+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T11:03:40.488+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-03T11:03:40.968+07:00  INFO 2554 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:40.975+07:00  INFO 2554 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-03T11:03:40.976+07:00  INFO 2554 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-03T11:03:40.997+07:00  INFO 2554 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-03T11:03:41.007+07:00  WARN 2554 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-03T11:03:41.497+07:00  INFO 2554 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-03T11:03:41.497+07:00  INFO 2554 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@55c8258e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-03T11:03:41.596+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-03T11:03:41.596+07:00  WARN 2554 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-03T11:03:41.917+07:00  INFO 2554 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T11:03:41.945+07:00  INFO 2554 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-03T11:03:41.950+07:00  INFO 2554 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-03T11:03:41.950+07:00  INFO 2554 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:03:41.956+07:00  WARN 2554 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:03:42.083+07:00  INFO 2554 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-03T11:03:42.535+07:00  INFO 2554 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T11:03:42.538+07:00  INFO 2554 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-03T11:03:42.571+07:00  INFO 2554 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-03T11:03:42.616+07:00  INFO 2554 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-03T11:03:42.689+07:00  INFO 2554 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-03T11:03:42.717+07:00  INFO 2554 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T11:03:42.741+07:00  INFO 2554 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1271117ms : this is harmless.
2025-09-03T11:03:42.749+07:00  INFO 2554 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-03T11:03:42.752+07:00  INFO 2554 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-03T11:03:42.763+07:00  INFO 2554 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 8394680ms : this is harmless.
2025-09-03T11:03:42.765+07:00  INFO 2554 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-03T11:03:42.786+07:00  INFO 2554 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-03T11:03:42.786+07:00  INFO 2554 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-03T11:03:43.808+07:00  INFO 2554 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-03T11:03:43.808+07:00  INFO 2554 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:03:43.809+07:00  WARN 2554 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:03:44.490+07:00  INFO 2554 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@11:00:00+0700 to 03/09/2025@11:15:00+0700
2025-09-03T11:03:44.490+07:00  INFO 2554 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@11:00:00+0700 to 03/09/2025@11:15:00+0700
2025-09-03T11:03:45.554+07:00  INFO 2554 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-03T11:03:45.554+07:00  INFO 2554 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:03:45.555+07:00  WARN 2554 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-03T11:03:45.816+07:00  INFO 2554 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-03T11:03:45.816+07:00  INFO 2554 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-03T11:03:45.816+07:00  INFO 2554 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-03T11:03:45.816+07:00  INFO 2554 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-03T11:03:45.816+07:00  INFO 2554 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-03T11:03:47.437+07:00  WARN 2554 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: df582dd8-7bde-4182-85d9-a9ec1aefe3e6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-03T11:03:47.441+07:00  INFO 2554 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-03T11:03:47.753+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T11:03:47.753+07:00  INFO 2554 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-03T11:03:47.754+07:00  INFO 2554 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-03T11:03:47.757+07:00  INFO 2554 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T11:03:47.757+07:00  INFO 2554 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T11:03:47.757+07:00  INFO 2554 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T11:03:47.806+07:00  INFO 2554 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03T11:03:47.806+07:00  INFO 2554 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-03T11:03:47.808+07:00  INFO 2554 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-03T11:03:47.816+07:00  INFO 2554 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@42d96a9e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T11:03:47.816+07:00  INFO 2554 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-03T11:03:47.817+07:00  INFO 2554 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-03T11:03:47.849+07:00  INFO 2554 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-03T11:03:47.849+07:00  INFO 2554 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-03T11:03:47.855+07:00  INFO 2554 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.654 seconds (process running for 13.931)
2025-09-03T11:03:58.805+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-03T11:04:06.790+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:04:50.884+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T11:04:50.912+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:04:59.681+07:00  INFO 2554 --- [qtp23426726-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0o643zflol4xu1pcfmjmpimqr31
2025-09-03T11:04:59.681+07:00  INFO 2554 --- [qtp23426726-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01omjueoh2wvt918hgzdqg02b5a0
2025-09-03T11:04:59.777+07:00  INFO 2554 --- [qtp23426726-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:04:59.778+07:00  INFO 2554 --- [qtp23426726-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01omjueoh2wvt918hgzdqg02b5a0, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:05:00.217+07:00  INFO 2554 --- [qtp23426726-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:05:00.219+07:00  INFO 2554 --- [qtp23426726-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:05:04.938+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:05:04.940+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:06:06.039+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:06:50.150+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-03T11:06:50.165+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:07:04.189+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:08:06.292+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:08:54.401+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-03T11:08:54.420+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:09:03.438+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:10:06.534+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:10:06.537+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:10:24.197+07:00  INFO 2554 --- [qtp23426726-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:10:24.199+07:00  INFO 2554 --- [qtp23426726-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:10:24.223+07:00  INFO 2554 --- [qtp23426726-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:10:24.223+07:00  INFO 2554 --- [qtp23426726-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:10:40.109+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:10:40.115+07:00  INFO 2554 --- [qtp23426726-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:10:40.117+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:10:40.155+07:00  INFO 2554 --- [qtp23426726-61] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:10:54.661+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-03T11:10:54.673+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:11:02.692+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:11:09.394+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:09.396+07:00  INFO 2554 --- [qtp23426726-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:10.919+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:10.919+07:00  INFO 2554 --- [qtp23426726-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:11.953+07:00  INFO 2554 --- [qtp23426726-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:11.991+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:13.411+07:00  INFO 2554 --- [qtp23426726-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:18.865+07:00  INFO 2554 --- [qtp23426726-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:11:18.865+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:12:05.819+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:12:53.964+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-03T11:12:53.973+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:13:06.994+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:13:27.417+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:13:27.432+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:13:27.492+07:00  INFO 2554 --- [qtp23426726-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:13:27.503+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:13:36.527+07:00  INFO 2554 --- [Scheduler-1879212387-1] n.d.m.session.AppHttpSessionListener     : The session node01omjueoh2wvt918hgzdqg02b5a0 is destroyed.
2025-09-03T11:13:42.655+07:00  INFO 2554 --- [qtp23426726-78] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T11:13:42.655+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T11:13:42.666+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T11:13:42.666+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SaleTaskReportService/salemanSystemPerformanceReport
2025-09-03T11:13:43.297+07:00  INFO 2554 --- [qtp23426726-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:13:43.298+07:00  INFO 2554 --- [qtp23426726-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:13:43.307+07:00  INFO 2554 --- [qtp23426726-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:13:43.307+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:13:45.466+07:00  INFO 2554 --- [qtp23426726-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:13:45.470+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:14:05.128+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:14:54.254+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-03T11:14:54.277+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:15:06.300+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:15:06.303+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:15:06.305+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T11:15:06.318+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@11:15:06+0700
2025-09-03T11:15:06.344+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@11:15:00+0700 to 03/09/2025@11:30:00+0700
2025-09-03T11:15:06.344+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@11:15:00+0700 to 03/09/2025@11:30:00+0700
2025-09-03T11:15:44.181+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:15:44.181+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:15:47.486+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:15:47.486+07:00  INFO 2554 --- [qtp23426726-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:16:04.460+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:16:53.559+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-03T11:16:53.565+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:17:06.586+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:18:03.691+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:18:52.794+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 4
2025-09-03T11:18:52.819+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:19:01.571+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:01.589+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:01.604+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:01.612+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:05.644+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:19:05.644+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:19:06.841+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:19:27.995+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:27.995+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:28.010+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:28.011+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:35.842+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:35.856+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:35.930+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:19:35.945+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:19:43.742+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:19:43.743+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:19:44.607+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:19:44.607+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:20:02.938+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:20:02.942+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:20:18.153+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:20:18.165+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:20:18.187+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:20:18.194+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:20:22.425+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:20:22.425+07:00  INFO 2554 --- [qtp23426726-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:20:45.208+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:20:45.210+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:20:45.224+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:20:45.224+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:20:50.261+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:20:50.261+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:20:52.060+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-09-03T11:20:52.077+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:21:04.578+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:21:04.584+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:21:04.599+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:21:04.600+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:21:06.113+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:21:08.280+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:21:08.280+07:00  INFO 2554 --- [qtp23426726-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:21:26.342+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:21:26.345+07:00  INFO 2554 --- [qtp23426726-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:21:26.370+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:21:26.370+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:21:30.857+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:21:30.857+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:22:02.206+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:22:51.304+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 2
2025-09-03T11:22:51.312+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:22:58.824+07:00  INFO 2554 --- [qtp23426726-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:22:58.825+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:22:58.839+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:22:58.839+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:23:01.583+07:00  INFO 2554 --- [qtp23426726-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:23:01.585+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:23:01.605+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:23:01.605+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:23:05.349+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:23:11.711+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:23:11.712+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:23:11.728+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:23:11.728+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:23:15.350+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:23:15.350+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:23:16.186+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:23:16.234+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:23:16.409+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:23:16.410+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:24:06.447+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:24:43.029+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:24:43.049+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:24:43.060+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:24:43.080+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:24:50.542+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 2
2025-09-03T11:24:50.550+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:25:03.007+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:03.008+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:03.032+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:03.032+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:04.578+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:25:04.579+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:25:24.989+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:24.990+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:25.000+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:25.001+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:26.988+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:26.989+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:25:26.998+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:26.998+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:25:31.016+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:25:31.016+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:26:06.689+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:26:26.329+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:26:26.331+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:26:26.352+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:26:26.352+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:26:34.281+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:26:34.316+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:26:34.318+07:00  INFO 2554 --- [qtp23426726-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:26:34.323+07:00  INFO 2554 --- [qtp23426726-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:26:38.703+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:26:38.707+07:00  INFO 2554 --- [qtp23426726-80] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:26:54.833+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 2
2025-09-03T11:26:54.846+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:27:03.870+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:28:06.966+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:28:27.475+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:27.477+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:27.490+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:27.490+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:42.212+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:42.225+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:42.238+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:42.252+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:46.066+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:46.067+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:46.082+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:46.082+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:52.911+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:52.915+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:28:52.924+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:52.924+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:28:54.059+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T11:28:54.062+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:29:01.224+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:29:01.224+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:29:02.128+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:29:02.128+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:29:03.070+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:29:26.797+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:26.798+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:26.823+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:26.823+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:44.214+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:44.214+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:44.224+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:44.224+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:45.010+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:45.019+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:45.043+07:00  INFO 2554 --- [qtp23426726-132] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:29:45.047+07:00  INFO 2554 --- [qtp23426726-132] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:29:48.393+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:29:48.393+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:30:06.162+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:30:06.165+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@11:30:06+0700
2025-09-03T11:30:06.177+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@11:30:00+0700 to 03/09/2025@11:45:00+0700
2025-09-03T11:30:06.178+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@11:30:00+0700 to 03/09/2025@11:45:00+0700
2025-09-03T11:30:06.178+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T11:30:06.179+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:30:54.264+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 2
2025-09-03T11:30:54.285+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:31:02.305+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:31:38.134+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:38.135+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:38.139+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:38.139+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:39.012+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:39.013+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:39.017+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:39.018+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:47.790+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:47.791+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:47.795+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:47.795+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:48.014+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:48.015+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:31:48.017+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:48.019+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:31:51.843+07:00  INFO 2554 --- [qtp23426726-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:31:51.843+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:32:05.400+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:32:18.105+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:18.105+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:18.108+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:18.108+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:20.307+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:20.308+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:20.311+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:20.314+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:21.012+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:21.013+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:32:21.021+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:21.021+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:32:24.383+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:32:24.383+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:32:54.521+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-09-03T11:32:54.539+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:33:06.559+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:34:04.660+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:34:53.745+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-03T11:34:53.758+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:35:06.775+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:35:06.777+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:35:55.676+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:35:55.676+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:35:55.758+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:35:55.784+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:35:56.017+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:35:56.017+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:35:56.027+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:35:56.027+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:03.865+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:36:15.459+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:15.459+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:15.463+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:15.469+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:16.022+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:16.024+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:16.027+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:16.030+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:25.619+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:25.620+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:25.630+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:25.630+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:26.009+07:00  INFO 2554 --- [qtp23426726-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:26.010+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:26.018+07:00  INFO 2554 --- [qtp23426726-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:26.019+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:29.695+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:36:29.695+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:36:43.560+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:43.563+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:36:43.577+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:43.577+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:36:50.765+07:00  INFO 2554 --- [qtp23426726-159] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:36:50.765+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:36:52.986+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-03T11:36:53.036+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T11:37:06.056+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:37:19.624+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:19.644+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:19.682+07:00  INFO 2554 --- [qtp23426726-79] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:19.691+07:00  INFO 2554 --- [qtp23426726-79] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:19.735+07:00 ERROR 2554 --- [qtp23426726-75] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-03T11:37:19.735+07:00 ERROR 2554 --- [qtp23426726-130] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-03T11:37:19.737+07:00 ERROR 2554 --- [qtp23426726-75] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T11:37:19.742+07:00  INFO 2554 --- [qtp23426726-75] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-03T11:37:19.738+07:00 ERROR 2554 --- [qtp23426726-130] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T11:37:19.743+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-03T11:37:22.011+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:22.012+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:22.027+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:22.038+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:25.705+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:37:25.707+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:37:48.614+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:48.622+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:48.645+07:00  INFO 2554 --- [qtp23426726-128] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:37:48.649+07:00  INFO 2554 --- [qtp23426726-128] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:37:52.187+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:37:52.187+07:00  INFO 2554 --- [qtp23426726-159] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:03.161+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:38:16.866+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:38:16.875+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:38:16.896+07:00  INFO 2554 --- [qtp23426726-159] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:38:16.907+07:00  INFO 2554 --- [qtp23426726-159] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:38:22.542+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:22.543+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:22.877+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:22.877+07:00  INFO 2554 --- [qtp23426726-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:23.417+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:23.457+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:38:43.994+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:38:44.003+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:38:44.011+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:38:44.029+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:38:52.295+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-03T11:38:52.317+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:39:06.339+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:39:12.064+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:39:12.066+07:00  INFO 2554 --- [qtp23426726-79] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:39:12.079+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:39:12.079+07:00  INFO 2554 --- [qtp23426726-79] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:39:13.940+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:39:13.940+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:39:13.945+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:39:13.945+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:39:18.456+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:39:18.456+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:02.447+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:40:02.453+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:40:12.291+07:00  INFO 2554 --- [qtp23426726-79] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:12.293+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:12.311+07:00  INFO 2554 --- [qtp23426726-79] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:12.312+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:20.913+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:20.926+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:21.162+07:00  INFO 2554 --- [qtp23426726-79] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:21.164+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:21.695+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:21.748+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:34.142+07:00  INFO 2554 --- [qtp23426726-79] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:34.155+07:00  INFO 2554 --- [qtp23426726-79] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:34.174+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:34.185+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:37.850+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:37.851+07:00  INFO 2554 --- [qtp23426726-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:51.577+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 3
2025-09-03T11:40:51.615+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-03T11:40:56.202+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:56.203+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:40:56.222+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:56.222+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:40:59.680+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:40:59.680+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:41:05.643+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:42:06.724+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:42:47.925+07:00  INFO 2554 --- [qtp23426726-130] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:42:47.926+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:42:47.933+07:00  INFO 2554 --- [qtp23426726-130] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:42:47.933+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:42:50.839+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T11:42:50.841+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:42:55.432+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:42:55.433+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:42:55.685+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:42:55.735+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:42:57.615+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:42:57.615+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:43:04.860+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:44:05.031+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:44:05.031+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:44:05.042+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:44:05.042+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:44:06.988+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:44:09.189+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:09.192+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:09.606+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:09.606+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:09.966+07:00  INFO 2554 --- [qtp23426726-75] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:09.966+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:10.373+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:10.407+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:32.368+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:44:32.369+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:44:32.377+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:44:32.378+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:44:43.471+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:43.471+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:43.772+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:43.820+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:45.152+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:45.152+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:44:50.104+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 5
2025-09-03T11:44:50.118+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:45:04.147+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:45:04.149+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:45:04.151+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T11:45:04.152+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@11:45:04+0700
2025-09-03T11:45:04.165+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@11:45:00+0700 to 03/09/2025@12:00:00+0700
2025-09-03T11:45:04.166+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@11:45:00+0700 to 03/09/2025@12:00:00+0700
2025-09-03T11:45:16.082+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:45:16.082+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:45:19.285+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:45:19.285+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:45:38.746+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:45:38.749+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:45:38.755+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:45:38.755+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:46:06.259+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:46:10.439+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:46:10.440+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:46:10.447+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:46:10.447+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:46:22.326+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:46:22.327+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:46:22.334+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:46:22.334+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:46:54.394+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-03T11:46:54.409+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:47:03.430+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:47:29.975+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:47:29.976+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:47:29.990+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:47:29.990+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:47:36.146+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:47:36.150+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:47:36.174+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:47:36.174+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:47:44.689+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:44.690+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:45.782+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:45.782+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:46.101+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:46.140+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:46.440+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:46.440+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:47.255+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:47.255+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:47.914+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:47:47.914+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:48:06.536+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:48:41.547+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:48:41.548+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:48:41.556+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:48:41.556+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:48:46.652+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:48:46.652+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:48:54.674+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 3
2025-09-03T11:48:54.680+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:49:02.698+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:50:05.798+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:50:05.802+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:50:54.907+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-03T11:50:54.936+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:51:06.954+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:52:05.048+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:52:37.806+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:52:37.807+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:52:37.812+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:52:37.812+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:52:54.168+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 2
2025-09-03T11:52:54.179+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:52:56.343+07:00  INFO 2554 --- [qtp23426726-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:52:56.343+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:52:56.356+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:52:56.356+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:52:59.291+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:52:59.292+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:52:59.580+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:52:59.580+07:00  INFO 2554 --- [qtp23426726-190] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:53:00.207+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:53:00.240+07:00  INFO 2554 --- [qtp23426726-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:53:06.195+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:54:04.297+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:54:16.393+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:54:16.394+07:00  INFO 2554 --- [qtp23426726-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:54:16.410+07:00  INFO 2554 --- [qtp23426726-65] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:54:16.410+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:54:53.408+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-03T11:54:53.424+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:55:06.441+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:55:06.443+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T11:55:34.520+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:55:34.551+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:55:35.131+07:00  INFO 2554 --- [qtp23426726-262] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:55:35.131+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:56:03.543+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:56:04.601+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:04.602+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:04.621+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:04.621+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:09.332+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:56:09.332+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:56:26.025+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:26.028+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:26.033+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:26.038+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:29.265+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:29.281+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:29.292+07:00  INFO 2554 --- [qtp23426726-191] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:29.300+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:35.061+07:00  INFO 2554 --- [qtp23426726-191] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:35.067+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:35.109+07:00  INFO 2554 --- [qtp23426726-262] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:35.129+07:00 ERROR 2554 --- [qtp23426726-74] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-03T11:56:35.130+07:00 ERROR 2554 --- [qtp23426726-232] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-03T11:56:35.131+07:00 ERROR 2554 --- [qtp23426726-74] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T11:56:35.138+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-03T11:56:35.131+07:00 ERROR 2554 --- [qtp23426726-232] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T11:56:35.139+07:00  INFO 2554 --- [qtp23426726-232] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-03T11:56:35.161+07:00  INFO 2554 --- [qtp23426726-262] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:37.598+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:37.606+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:37.634+07:00  INFO 2554 --- [qtp23426726-190] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:37.644+07:00  INFO 2554 --- [qtp23426726-190] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:38.072+07:00  INFO 2554 --- [qtp23426726-232] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:38.073+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:56:38.075+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:38.075+07:00  INFO 2554 --- [qtp23426726-232] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:56:52.645+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-09-03T11:56:52.653+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:57:06.681+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:57:11.645+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:11.647+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:11.648+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:11.650+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:13.069+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:13.071+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:13.086+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:13.100+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:26.861+07:00  INFO 2554 --- [qtp23426726-191] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:26.862+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:26.870+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:26.870+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:28.056+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:28.057+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:57:28.061+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:28.061+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:57:30.869+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:57:30.869+07:00  INFO 2554 --- [qtp23426726-232] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:58:02.787+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T11:58:07.927+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:58:07.934+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:58:07.959+07:00  INFO 2554 --- [qtp23426726-261] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:58:07.969+07:00  INFO 2554 --- [qtp23426726-261] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:58:09.057+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:58:09.058+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T11:58:09.067+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:58:09.078+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T11:58:22.578+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:58:22.578+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:58:22.880+07:00  INFO 2554 --- [qtp23426726-232] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:58:22.884+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T11:58:51.889+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-03T11:58:51.904+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T11:59:05.927+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:00:02.014+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:00:02.016+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:00:07.021+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T12:00:07.022+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@12:00:07+0700
2025-09-03T12:00:07.030+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@12:00:00+0700 to 03/09/2025@12:15:00+0700
2025-09-03T12:00:07.030+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@12:00:00+0700 to 03/09/2025@12:15:00+0700
2025-09-03T12:00:07.039+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-03T12:00:07.040+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-09-03T12:00:07.041+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-03T12:00:51.150+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 1
2025-09-03T12:00:51.166+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:01:05.188+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:02:06.312+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:02:06.378+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:02:06.379+07:00  INFO 2554 --- [qtp23426726-191] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:02:06.382+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:02:06.382+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:02:07.060+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:02:07.062+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:02:07.069+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:02:07.072+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:02:50.385+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:02:50.390+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:03:04.409+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:03:12.541+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:12.542+07:00  INFO 2554 --- [qtp23426726-191] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:12.549+07:00  INFO 2554 --- [qtp23426726-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:12.549+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:13.106+07:00  INFO 2554 --- [qtp23426726-262] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:13.115+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:13.179+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:13.179+07:00  INFO 2554 --- [qtp23426726-262] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:16.396+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:16.397+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:16.400+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:16.405+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:17.063+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:17.069+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:17.097+07:00  INFO 2554 --- [qtp23426726-297] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:17.104+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:43.110+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:43.111+07:00  INFO 2554 --- [qtp23426726-297] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:43.121+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:43.121+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:44.076+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:44.076+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:03:44.083+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:44.085+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:03:48.237+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:03:48.237+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:04:03.493+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:04:03.494+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:04:03.499+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:04:03.501+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:04:06.525+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:04:11.371+07:00  INFO 2554 --- [qtp23426726-82] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:04:11.372+07:00  INFO 2554 --- [qtp23426726-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:04:11.394+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:04:11.394+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:04:16.963+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:04:16.963+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:04:54.638+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-09-03T12:04:54.658+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:05:03.675+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:05:03.677+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:05:03.918+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:05:03.919+07:00  INFO 2554 --- [qtp23426726-297] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:05:03.924+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:05:03.924+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:05:09.697+07:00  INFO 2554 --- [qtp23426726-262] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:09.697+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:12.571+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:12.572+07:00  INFO 2554 --- [qtp23426726-288] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:12.839+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:12.839+07:00  INFO 2554 --- [qtp23426726-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:13.383+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:13.421+07:00  INFO 2554 --- [qtp23426726-288] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:26.142+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:05:26.144+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:02.533+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:02.533+07:00  INFO 2554 --- [qtp23426726-297] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:06.784+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:06:22.182+07:00  INFO 2554 --- [qtp23426726-188] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:06:22.184+07:00  INFO 2554 --- [qtp23426726-177] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:06:22.303+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:06:22.312+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:06:26.030+07:00  INFO 2554 --- [qtp23426726-82] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:26.034+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:48.324+07:00  INFO 2554 --- [qtp23426726-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:06:48.325+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T12:06:48.351+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:06:48.351+07:00  INFO 2554 --- [qtp23426726-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T12:06:52.109+07:00  INFO 2554 --- [qtp23426726-188] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:52.109+07:00  INFO 2554 --- [qtp23426726-177] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T12:06:54.892+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 2
2025-09-03T12:06:54.908+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:07:02.929+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:08:06.072+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:08:54.217+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T12:08:54.222+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:09:02.242+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:10:05.393+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:10:05.395+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:10:54.543+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T12:10:54.557+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:11:06.571+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:12:04.726+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:12:53.864+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:12:53.866+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:13:06.891+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:14:04.039+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:14:53.188+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:14:53.192+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:15:06.219+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:15:06.219+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:15:06.220+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T12:15:06.220+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@12:15:06+0700
2025-09-03T12:15:06.237+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@12:15:00+0700 to 03/09/2025@12:30:00+0700
2025-09-03T12:15:06.237+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@12:15:00+0700 to 03/09/2025@12:30:00+0700
2025-09-03T12:16:03.433+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:16:52.563+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 11
2025-09-03T12:16:52.566+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:17:06.592+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:18:02.731+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:18:51.887+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:18:51.892+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:19:05.930+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:20:02.112+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:20:02.114+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:20:51.271+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:20:51.272+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:21:05.302+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:22:06.498+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:22:50.628+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:22:50.633+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:23:04.669+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:24:06.845+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:24:54.966+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:24:54.972+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:25:04.006+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:25:04.007+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:26:06.185+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:26:54.336+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-03T12:26:54.338+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:27:03.378+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:28:06.550+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:28:54.712+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:28:54.718+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:29:02.739+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:30:05.924+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:30:05.926+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:30:05.927+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T12:30:05.928+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@12:30:05+0700
2025-09-03T12:30:05.943+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@12:30:00+0700 to 03/09/2025@12:45:00+0700
2025-09-03T12:30:05.943+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@12:30:00+0700 to 03/09/2025@12:45:00+0700
2025-09-03T12:30:54.084+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:30:54.089+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:31:02.109+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:32:05.243+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:32:54.394+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:32:54.400+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:33:06.432+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:34:04.587+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:34:53.714+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:34:53.717+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:35:06.753+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:35:06.754+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:36:03.923+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:36:53.081+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:36:53.089+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:37:06.121+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:38:03.277+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:38:52.399+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:38:52.402+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:39:06.429+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:40:02.578+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:40:02.579+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:40:51.724+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T12:40:51.729+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:41:05.775+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:42:06.953+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:42:51.089+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:42:51.092+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:43:05.123+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:44:06.281+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:44:50.417+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:44:50.422+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:45:04.475+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:45:04.477+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:45:04.477+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T12:45:04.478+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@12:45:04+0700
2025-09-03T12:45:04.504+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@12:45:00+0700 to 03/09/2025@13:00:00+0700
2025-09-03T12:45:04.504+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@12:45:00+0700 to 03/09/2025@13:00:00+0700
2025-09-03T12:46:06.703+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:46:54.844+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T12:46:54.850+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:47:03.888+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:48:06.058+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:48:54.147+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:48:54.150+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:49:03.182+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:50:06.375+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:50:06.376+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:50:54.501+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:50:54.505+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:51:02.540+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:52:05.698+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:52:54.839+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T12:52:54.844+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:53:06.878+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:54:05.047+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:54:54.184+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:54:54.186+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:55:06.200+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:55:06.200+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T12:56:04.379+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:56:53.539+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T12:56:53.546+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:57:06.582+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:58:03.755+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T12:58:52.904+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:58:52.905+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T12:59:06.945+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:00:03.082+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:00:03.083+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:00:03.084+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T13:00:03.084+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@13:00:03+0700
2025-09-03T13:00:03.093+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@13:00:00+0700 to 03/09/2025@13:15:00+0700
2025-09-03T13:00:03.093+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@13:00:00+0700 to 03/09/2025@13:15:00+0700
2025-09-03T13:00:03.093+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-03T13:00:52.216+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T13:00:52.224+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:01:06.268+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:02:02.452+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:02:51.578+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:02:51.580+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:03:05.618+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:04:06.741+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:04:50.887+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:04:50.890+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:05:04.938+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:05:04.939+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:06:06.127+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:06:50.280+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:06:50.283+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:07:04.306+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:08:06.467+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:08:54.594+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:08:54.598+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:09:03.629+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:10:06.775+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:10:06.776+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:10:54.917+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T13:10:54.921+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:11:02.933+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:12:06.099+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:12:54.228+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:12:54.232+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:13:02.253+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:14:05.443+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:14:54.597+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:14:54.601+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:15:06.636+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:15:06.637+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T13:15:06.638+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@13:15:06+0700
2025-09-03T13:15:06.653+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@13:15:00+0700 to 03/09/2025@13:30:00+0700
2025-09-03T13:15:06.654+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@13:15:00+0700 to 03/09/2025@13:30:00+0700
2025-09-03T13:15:06.654+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:16:04.838+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:16:53.997+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:16:53.999+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:17:06.031+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:18:04.198+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:18:53.319+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:18:53.323+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:19:06.359+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:20:03.561+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:20:03.563+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:20:52.717+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:20:52.720+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:21:06.753+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:22:02.939+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:22:52.064+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:22:52.071+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:23:06.113+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:24:02.255+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:24:51.386+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:24:51.389+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:25:05.424+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:25:05.425+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:26:06.596+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:26:50.730+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T13:26:50.735+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:27:04.770+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:28:06.944+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:28:50.073+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:28:50.076+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:29:04.120+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:30:06.309+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:30:06.310+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:30:06.310+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T13:30:06.310+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@13:30:06+0700
2025-09-03T13:30:06.320+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@13:30:00+0700 to 03/09/2025@13:45:00+0700
2025-09-03T13:30:06.320+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@13:30:00+0700 to 03/09/2025@13:45:00+0700
2025-09-03T13:30:54.491+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T13:30:54.495+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:31:03.533+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:32:06.720+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:32:54.849+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:32:54.854+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:33:02.885+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:34:06.058+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:34:54.209+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:34:54.213+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:35:02.244+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:35:02.245+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:36:05.439+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:36:54.609+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:36:54.616+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:37:06.644+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:38:04.827+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:38:53.963+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:38:53.966+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:39:07.002+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:40:04.607+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:40:04.632+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:40:53.834+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-03T13:40:53.853+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:41:06.877+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:41:41.048+07:00  INFO 2554 --- [qtp23426726-728] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:41:41.048+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:41:41.719+07:00  INFO 2554 --- [qtp23426726-597] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:41:41.727+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:42:03.965+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:42:53.056+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T13:42:53.072+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:43:06.093+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:44:03.180+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:44:52.295+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:44:52.310+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:45:06.333+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:45:06.334+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:45:06.334+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T13:45:06.335+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@13:45:06+0700
2025-09-03T13:45:06.356+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@13:45:00+0700 to 03/09/2025@14:00:00+0700
2025-09-03T13:45:06.357+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@13:45:00+0700 to 03/09/2025@14:00:00+0700
2025-09-03T13:46:02.449+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:46:51.504+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T13:46:51.506+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:47:05.532+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:47:39.811+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:47:39.812+07:00  INFO 2554 --- [qtp23426726-963] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:47:39.916+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:47:39.941+07:00  INFO 2554 --- [qtp23426726-963] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:47:46.242+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:47:46.250+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:47:46.273+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:47:46.277+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:48:06.643+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:48:50.739+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-03T13:48:50.755+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:49:04.782+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:50:06.614+07:00  INFO 2554 --- [qtp23426726-721] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:06.631+07:00  INFO 2554 --- [qtp23426726-721] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:06.633+07:00  INFO 2554 --- [qtp23426726-1013] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:06.638+07:00  INFO 2554 --- [qtp23426726-1013] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:06.866+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:50:06.867+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:50:13.540+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:13.548+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:13.562+07:00  INFO 2554 --- [qtp23426726-1020] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:13.567+07:00  INFO 2554 --- [qtp23426726-1020] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:17.650+07:00  INFO 2554 --- [qtp23426726-641] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:17.658+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:17.671+07:00  INFO 2554 --- [qtp23426726-1019] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:50:17.679+07:00  INFO 2554 --- [qtp23426726-1019] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:50:26.278+07:00  INFO 2554 --- [qtp23426726-1019] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:50:26.278+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:50:54.974+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-03T13:50:54.989+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:51:04.010+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:52:06.110+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:52:54.191+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-03T13:52:54.198+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:53:03.213+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:53:43.938+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:53:43.952+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:53:43.974+07:00  INFO 2554 --- [qtp23426726-1017] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:53:43.988+07:00  INFO 2554 --- [qtp23426726-1017] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:54:02.121+07:00  INFO 2554 --- [qtp23426726-1017] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:54:02.136+07:00  INFO 2554 --- [qtp23426726-1017] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:54:02.151+07:00  INFO 2554 --- [qtp23426726-1015] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:54:02.163+07:00  INFO 2554 --- [qtp23426726-1015] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:54:06.304+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:54:06.388+07:00  INFO 2554 --- [qtp23426726-1017] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:54:06.388+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:54:06.941+07:00  INFO 2554 --- [qtp23426726-1017] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:54:06.944+07:00  INFO 2554 --- [qtp23426726-597] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:54:54.426+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-03T13:54:54.441+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:55:02.455+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T13:55:02.456+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:55:35.457+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:35.460+07:00  INFO 2554 --- [qtp23426726-1015] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:35.471+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:35.484+07:00  INFO 2554 --- [qtp23426726-1015] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:42.162+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:42.171+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:42.191+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:42.198+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:50.335+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:50.336+07:00  INFO 2554 --- [qtp23426726-1022] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:50.397+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:50.397+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:59.080+07:00  INFO 2554 --- [qtp23426726-641] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:59.081+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:55:59.105+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:55:59.105+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:56:05.565+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:56:14.325+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:56:14.325+07:00  INFO 2554 --- [qtp23426726-721] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:56:28.679+07:00  INFO 2554 --- [qtp23426726-641] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:56:28.748+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:56:28.758+07:00  INFO 2554 --- [qtp23426726-597] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:56:28.767+07:00  INFO 2554 --- [qtp23426726-597] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:56:32.869+07:00  INFO 2554 --- [qtp23426726-1076] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:56:32.869+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:56:54.684+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-09-03T13:56:54.706+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:57:06.720+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:57:29.936+07:00  INFO 2554 --- [qtp23426726-597] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:57:29.942+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:57:29.950+07:00  INFO 2554 --- [qtp23426726-597] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:57:29.950+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:57:49.698+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:57:49.699+07:00  INFO 2554 --- [qtp23426726-641] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:57:49.705+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:57:49.705+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:58:04.814+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:58:53.880+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:58:53.887+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T13:59:06.908+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T13:59:07.127+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:59:07.127+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:59:25.689+07:00  INFO 2554 --- [qtp23426726-721] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:59:25.696+07:00  INFO 2554 --- [qtp23426726-721] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:59:25.730+07:00  INFO 2554 --- [qtp23426726-1018] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T13:59:25.734+07:00  INFO 2554 --- [qtp23426726-1018] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T13:59:35.084+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T13:59:35.084+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:00:03.987+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-03T14:00:03.988+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T14:00:03.988+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@14:00:03+0700
2025-09-03T14:00:04.002+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@14:00:00+0700 to 03/09/2025@14:15:00+0700
2025-09-03T14:00:04.002+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@14:00:00+0700 to 03/09/2025@14:15:00+0700
2025-09-03T14:00:04.003+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:00:04.003+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:00:04.012+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-09-03T14:00:21.937+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:00:21.938+07:00  INFO 2554 --- [qtp23426726-1018] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:00:22.009+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:00:22.019+07:00  INFO 2554 --- [qtp23426726-1018] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:00:31.438+07:00  INFO 2554 --- [qtp23426726-1020] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:00:31.438+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:00:39.797+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:00:39.797+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:00:53.096+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-03T14:00:53.102+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:01:01.630+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:01.631+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:01.638+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:01.638+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:05.138+07:00  INFO 2554 --- [qtp23426726-1020] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:01:05.138+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:01:06.120+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:01:13.830+07:00  INFO 2554 --- [qtp23426726-1020] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:13.830+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:13.834+07:00  INFO 2554 --- [qtp23426726-1020] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:13.834+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:15.519+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:15.530+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:01:15.536+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:15.539+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:01:19.556+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:01:19.556+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:02:03.220+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:02:51.218+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:51.218+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:51.250+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:02:51.250+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:02:52.323+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-03T14:02:52.330+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:02:54.946+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:54.949+07:00  INFO 2554 --- [qtp23426726-597] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:54.952+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:02:54.970+07:00  INFO 2554 --- [qtp23426726-597] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:02:59.018+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:59.019+07:00  INFO 2554 --- [qtp23426726-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:02:59.027+07:00  INFO 2554 --- [qtp23426726-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:02:59.027+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:03:03.418+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:03:03.419+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:03:06.356+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:04:02.450+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:04:51.598+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-09-03T14:04:51.622+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:05:05.644+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:05:05.645+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:06:06.716+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:06:50.781+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-03T14:06:50.792+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:07:04.814+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:08:06.903+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:08:49.994+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T14:08:50.003+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:09:04.025+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:10:06.112+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:10:06.115+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:10:54.212+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-03T14:10:54.221+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:11:03.237+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:12:06.336+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:12:54.428+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-03T14:12:54.435+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:13:02.452+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:14:05.553+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:14:54.635+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 4
2025-09-03T14:14:54.641+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:15:06.655+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:15:06.657+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T14:15:06.657+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@14:15:06+0700
2025-09-03T14:15:06.664+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@14:15:00+0700 to 03/09/2025@14:30:00+0700
2025-09-03T14:15:06.665+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@14:15:00+0700 to 03/09/2025@14:30:00+0700
2025-09-03T14:15:06.667+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:16:04.762+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:16:53.911+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-03T14:16:53.958+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:17:06.974+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:17:38.666+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:17:38.667+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:17:38.778+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:17:38.786+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:18:04.119+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:18:26.279+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:18:26.307+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:18:26.319+07:00  INFO 2554 --- [qtp23426726-1224] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:18:26.361+07:00 ERROR 2554 --- [qtp23426726-942] n.d.m.monitor.call.EndpointCallContext   : Start call with component JobTrackingService, method searchJobTrackingProjects, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : { },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "orderBy" : {
    "fields" : [ "modifiedTime", "createdTime", "label" ],
    "selectFields" : [ "label" ],
    "sort" : "ASC"
  },
  "maxReturn" : 1000
} ]
2025-09-03T14:18:26.361+07:00 ERROR 2554 --- [qtp23426726-925] n.d.m.monitor.call.EndpointCallContext   : Start call with component JobTrackingService, method searchJobTrackingProjects, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : { },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "orderBy" : {
    "fields" : [ "modifiedTime", "createdTime", "label" ],
    "selectFields" : [ "label" ],
    "sort" : "ASC"
  },
  "maxReturn" : 1000
} ]
2025-09-03T14:18:26.361+07:00 ERROR 2554 --- [qtp23426726-925] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at cloud.datatp.jobtracking.JobTrackingProjectLogic.searchJobTrackingProjects(JobTrackingProjectLogic.java:146)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackingProjects(JobTrackingService.java:90)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackingProjects(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T14:18:26.361+07:00 ERROR 2554 --- [qtp23426726-942] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at cloud.datatp.jobtracking.JobTrackingProjectLogic.searchJobTrackingProjects(JobTrackingProjectLogic.java:146)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackingProjects(JobTrackingService.java:90)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackingProjects(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-03T14:18:26.365+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint JobTrackingService/searchJobTrackingProjects
2025-09-03T14:18:26.369+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint JobTrackingService/searchJobTrackingProjects
2025-09-03T14:18:26.369+07:00  INFO 2554 --- [qtp23426726-1224] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:18:29.298+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:18:29.300+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:18:29.471+07:00  INFO 2554 --- [qtp23426726-1225] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:18:29.484+07:00  INFO 2554 --- [qtp23426726-1225] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:18:53.331+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-03T14:18:53.367+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:19:06.383+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:19:36.529+07:00  INFO 2554 --- [qtp23426726-1227] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:19:36.531+07:00  INFO 2554 --- [qtp23426726-1222] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:19:36.546+07:00  INFO 2554 --- [qtp23426726-1227] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:19:36.579+07:00  INFO 2554 --- [qtp23426726-1222] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:20:03.496+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:20:03.498+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:20:19.919+07:00  INFO 2554 --- [qtp23426726-925] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:20:19.926+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:20:19.934+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:20:19.941+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:20:29.501+07:00  INFO 2554 --- [qtp23426726-1227] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:20:29.504+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:20:29.513+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:20:29.513+07:00  INFO 2554 --- [qtp23426726-1227] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:20:52.629+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-03T14:20:52.643+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:21:06.662+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:21:10.510+07:00  INFO 2554 --- [qtp23426726-641] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:21:10.510+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:21:10.908+07:00  INFO 2554 --- [qtp23426726-1225] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:21:10.908+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:21:38.545+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:21:38.579+07:00  INFO 2554 --- [qtp23426726-1245] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:21:38.584+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:21:38.588+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:21:43.042+07:00  INFO 2554 --- [qtp23426726-942] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:21:43.042+07:00  INFO 2554 --- [qtp23426726-1225] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:21:43.046+07:00  INFO 2554 --- [qtp23426726-942] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:21:43.048+07:00  INFO 2554 --- [qtp23426726-1225] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:21:50.046+07:00  INFO 2554 --- [qtp23426726-1225] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:21:50.048+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:22:03.009+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:22:09.333+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:22:09.343+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:22:52.133+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-03T14:22:52.163+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:23:06.190+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:23:28.432+07:00  INFO 2554 --- [qtp23426726-1245] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:23:28.433+07:00  INFO 2554 --- [qtp23426726-1022] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:23:28.441+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:23:28.441+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:23:32.609+07:00  INFO 2554 --- [qtp23426726-1222] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:23:32.609+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:24:02.273+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:24:51.350+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:24:51.367+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:24:57.383+07:00  INFO 2554 --- [qtp23426726-1021] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:24:57.385+07:00  INFO 2554 --- [qtp23426726-1022] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:24:57.393+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:24:57.394+07:00  INFO 2554 --- [qtp23426726-1021] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:05.400+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:25:05.403+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:25:14.782+07:00  INFO 2554 --- [qtp23426726-1245] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:14.782+07:00  INFO 2554 --- [qtp23426726-1222] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:14.813+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:14.814+07:00  INFO 2554 --- [qtp23426726-1222] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:26.481+07:00  INFO 2554 --- [qtp23426726-1022] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:26.493+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:26.506+07:00  INFO 2554 --- [qtp23426726-1223] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:26.525+07:00  INFO 2554 --- [qtp23426726-1223] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:31.502+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:25:31.505+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:25:55.150+07:00  INFO 2554 --- [qtp23426726-1022] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:55.155+07:00  INFO 2554 --- [qtp23426726-1077] n.d.module.session.ClientSessionManager  : Add a client session id = node0o643zflol4xu1pcfmjmpimqr31, token = 36fab6a5a4c7bb2f510b15f11541d659
2025-09-03T14:25:55.161+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:55.162+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-03T14:25:59.297+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:25:59.297+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:26:06.515+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:26:42.938+07:00  INFO 2554 --- [qtp23426726-1022] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:26:42.941+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:26:46.610+07:00  INFO 2554 --- [qtp23426726-925] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:26:46.619+07:00  INFO 2554 --- [qtp23426726-1077] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-03T14:26:50.620+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 2
2025-09-03T14:26:50.635+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:27:04.654+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:28:06.754+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:28:54.845+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-03T14:28:54.870+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:29:03.889+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:30:02.140+07:00 ERROR 2554 --- [qtp23426726-1222] n.d.m.monitor.call.EndpointCallContext   : Start call with component SecurityService, method searchAccessTokens, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ "modified_time" ],
    "sort" : "DESC"
  },
  "maxReturn" : 1000
} ]
2025-09-03T14:30:02.140+07:00 ERROR 2554 --- [qtp23426726-1245] n.d.m.monitor.call.EndpointCallContext   : Start call with component SecurityService, method searchAccessTokens, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "36fab6a5a4c7bb2f510b15f11541d659",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0o643zflol4xu1pcfmjmpimqr31",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ "modified_time" ],
    "sort" : "DESC"
  },
  "maxReturn" : 1000
} ]
2025-09-03T14:30:02.142+07:00 ERROR 2554 --- [qtp23426726-1222] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
        SELECT
          at.*,
          aa.full_name   AS user_full_name
        FROM security_access_token at
        LEFT JOIN account_account aa ON aa.id = at.account_id
        WHERE
          (storage_state IN (?))
          -- AND  account_id = :accountId
          -- AND  (label ILIKE 'null' OR login_id ILIKE 'null')
        ORDER BY modified_time DESC
        LIMIT 1000
      ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.data.db.repository.DAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createSqlSelectView(SqlQueryUnitManager.java:51)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:126)
	at net.datatp.module.core.security.SecurityLogic.searchAccessTokens(SecurityLogic.java:214)
	at net.datatp.module.core.security.SecurityService.searchAccessTokens(SecurityService.java:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.core.security.SecurityService$$SpringCGLIB$$0.searchAccessTokens(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: column reference "storage_state" is ambiguous
  Position: 201
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 127 common frames omitted

2025-09-03T14:30:02.149+07:00  INFO 2554 --- [qtp23426726-1222] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SecurityService/searchAccessTokens
2025-09-03T14:30:02.142+07:00 ERROR 2554 --- [qtp23426726-1245] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
        SELECT
          at.*,
          aa.full_name   AS user_full_name
        FROM security_access_token at
        LEFT JOIN account_account aa ON aa.id = at.account_id
        WHERE
          (storage_state IN (?))
          -- AND  account_id = :accountId
          -- AND  (label ILIKE 'null' OR login_id ILIKE 'null')
        ORDER BY modified_time DESC
        LIMIT 1000
      ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.data.db.repository.DAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createSqlSelectView(SqlQueryUnitManager.java:51)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:126)
	at net.datatp.module.core.security.SecurityLogic.searchAccessTokens(SecurityLogic.java:214)
	at net.datatp.module.core.security.SecurityService.searchAccessTokens(SecurityService.java:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.core.security.SecurityService$$SpringCGLIB$$0.searchAccessTokens(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: column reference "storage_state" is ambiguous
  Position: 201
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 127 common frames omitted

2025-09-03T14:30:02.157+07:00  INFO 2554 --- [qtp23426726-1245] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SecurityService/searchAccessTokens
2025-09-03T14:30:06.989+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:30:06.990+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-03T14:30:06.991+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-03T14:30:06.991+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 03/09/2025@14:30:06+0700
2025-09-03T14:30:07.017+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 03/09/2025@14:30:00+0700 to 03/09/2025@14:45:00+0700
2025-09-03T14:30:07.017+07:00  INFO 2554 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 03/09/2025@14:30:00+0700 to 03/09/2025@14:45:00+0700
2025-09-03T14:30:54.102+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 6
2025-09-03T14:30:54.131+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:31:03.154+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:32:06.252+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:32:54.393+07:00  INFO 2554 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-03T14:32:54.400+07:00  INFO 2554 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:33:02.414+07:00  INFO 2554 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-03T14:33:06.484+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@42d96a9e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-03T14:33:06.485+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-03T14:33:06.486+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-03T14:33:06.486+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-03T14:33:06.486+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-03T14:33:06.486+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-03T14:33:06.502+07:00  INFO 2554 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-03T14:33:06.564+07:00  INFO 2554 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-03T14:33:06.571+07:00  INFO 2554 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-03T14:33:06.655+07:00  INFO 2554 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:06.657+07:00  INFO 2554 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:06.658+07:00  INFO 2554 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-03T14:33:06.658+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T14:33:06.659+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T14:33:06.659+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-03T14:33:06.660+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-03T14:33:06.660+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-03T14:33:06.660+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-03T14:33:06.660+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-03T14:33:06.661+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-03T14:33:06.661+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-03T14:33:06.661+07:00  INFO 2554 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-03T14:33:06.664+07:00  INFO 2554 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@73c21b13{STOPPING}[12.0.15,sto=0]
2025-09-03T14:33:06.669+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-03T14:33:06.672+07:00  INFO 2554 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@1d280bab{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16765833008127519605/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@12639753{STOPPED}}
