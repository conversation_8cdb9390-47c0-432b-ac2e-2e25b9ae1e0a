package communication.sql;

/**
 * Create RecipientMessage query that can filter by recipient, message tag....
 */

import net.datatp.module.data.db.query.SqlQueryParams;

def INNER_JOIN_FILTER_BY_TAG(SqlQueryParams params) {
  def tagName = params.getParam("tagName");
  if(tagName == null) return "";

  def query = """
    INNER JOIN message_recipient_message_tag_rel tagrel
      ON tagrel.recipient_message_id = rm.id
    INNER JOIN message_recipient_message_tag tag
      ON tagrel.recipient_message_tag_id = tag.id AND tag.name = '${tagName}'
  """
  return query;
}

def FILTER_BY_CHANNEL(SqlQueryParams params) {
  def channelName = params.getParam("channelName");
  if(channelName == null) return "";
  return "AND rm.channel_name = '${channelName}'  "
}


def FILTER_BY_UNREAD(SqlQueryParams params) {
  def unread = params.getParam("unread");
  if(unread) {
    return "AND rm.read = False"
  }
  return "";
}

def params = (SqlQueryParams) sqlparams;
def accountId = params.getParam("accountId");

def query = """
    SELECT
      rm.subject,

      rm.sender_account_id          AS sender_account_id,
      rm.recipient_account_id,
      rm.channel_name,
      rm.deliver_type,
      rm.read,

      rm.id,
      rm.modified_by,
      rm.modified_time      AS modified_time

    FROM
      message_message m,
      message_recipient_message rm
    ${INNER_JOIN_FILTER_BY_TAG(params)}

    WHERE
      rm.message_id = m.id
      AND rm.recipient_account_id  = ${accountId}
      ${FILTER_BY_CHANNEL(params)}
      ${FILTER_BY_UNREAD(params)}

    ORDER BY modified_time DESC
"""

return query;