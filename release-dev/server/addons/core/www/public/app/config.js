

var DATATP_CONFIG = {
  environment: 'dev',
  build: "latest",
  hosting: {
    domain: 'datatp.cloud'
  },

  // uiServerUrl: "https://datatp-1-uiproxy.dev.datatp.net",
  // serverUrl:   "https://datatp-1-proxy.dev.datatp.net",
  // restUrl:     "https://datatp-1-proxy.dev.datatp.net/rest/v1.0.0",
  // apiUrl:      "https://datatp-1-proxy.dev.datatp.net/api",

  //uiServerUrl: "https://datatp-2-uiproxy.dev.datatp.net",
  //serverUrl:   "https://datatp-2-proxy.dev.datatp.net",
  //restUrl:     "https://datatp-2-proxy.dev.datatp.net/rest/v1.0.0",
  //apiUrl:      "https://datatp-2-proxy.dev.datatp.net/api",

  // serverUrl: "https://beelogistics.cloud",
  // restUrl: "https://beelogistics.cloud/rest/v1.0.0",
  // apiUrl: "https://beelogistics.cloud/api",

  serverUrl: "http://localhost:7080",
  restUrl: "http://localhost:7080/rest/v1.0.0",
  apiUrl: "http://localhost:7080/api",
  chatbotRestUrl: "http://localhost:5005/webhooks/rest"
}
