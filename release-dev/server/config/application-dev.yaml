app:
  env: dev

logging:
  config: file:../server-env/config/datatp-logback-console.xml

server:
  servlet:
    session:
      timeout: 5m

---
spring:
  profiles:
    group:
      dev: database, db-schema-update, data, log-info-file
---
spring:
  profiles:
    group:
      dev-console: database, db-schema-validate, data
---
spring:
  profiles:
    group:
      dev-new: database, db-schema-update, data

---
spring:
  profiles:
    group:
      dev-update: database, db-schema-update, data

---
spring:
  config:
    activate:
      on-profile: db-schema-validate
  datasource:
    hibernate:
      hbm2ddl:
        auto: validate

---
spring:
  config:
    activate:
      on-profile: db-schema-update
  datasource:
    hibernate:
      hbm2ddl:
        auto: update
