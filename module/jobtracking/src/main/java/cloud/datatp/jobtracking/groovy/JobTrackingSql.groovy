package cloud.datatp.jobtracking.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder;
import net.datatp.util.ds.MapObject
import net.datatp.util.text.StringUtil;

public class JobTrackingSql extends Executor {
  public class SearchJobTracking extends ExecutableSqlBuilder {
    private String AND_SEARCH_FILTER(MapObject sqlParams) {
      String view = sqlParams.getString("view");
      String searchPattern = sqlParams.getString("searchPattern")
      if(StringUtil.isBlank(searchPattern)) return "";
      if ("claim".equals(view)) {
        return """
            AND (lgc_job_tracking.id IN 
            (SELECT job_tracking_id FROM lgc_job_tracking_cell WHERE string_value = '${searchPattern}' GROUP BY job_tracking_id))
         """;
      }
      return """
            AND (lgc_job_tracking.id IN 
            (SELECT job_tracking_id FROM lgc_job_tracking_cell WHERE string_value LIKE '%${searchPattern}%' GROUP BY job_tracking_id))
         """;
    }
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
          SELECT 
            lgc_job_tracking.job_tracking_project_id      AS job_tracking_project_id,
            lgc_job_tracking.id                           AS job_tracking_id,
            lgc_job_tracking.custom_print_document_date   AS custom_print_document_date,
            lgc_job_tracking.owner_account_id             AS owner_account_id,
            lgc_job_tracking.owner_account_full_name      AS owner_account_full_name,
            lgc_job_tracking.row_height                   AS row_height,
            lgc_job_tracking.step_done_count              AS step_done_count,
            lgc_job_tracking.last_step_name               AS last_step_name,
            count (lgc_job_tracking_claim.id)             AS claim_count
          FROM lgc_job_tracking
          LEFT JOIN lgc_job_tracking_claim ON lgc_job_tracking_claim.job_tracking_id = lgc_job_tracking.id
          WHERE
            ${FILTER_BY_STORAGE_STATE('lgc_job_tracking', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_job_tracking.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_job_tracking.job_tracking_project_id', 'jobTrackingProjectId', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_job_tracking.id', 'jobTrackingId', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_job_tracking.id', 'jobTrackingIds', sqlParams)}
            ${AND_SEARCH_FILTER(sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_job_tracking.owner_account_id', 'ownerAccountId', sqlParams)}
          GROUP BY 
            lgc_job_tracking.job_tracking_project_id,
            lgc_job_tracking.id,
            lgc_job_tracking.custom_print_document_date,
            lgc_job_tracking.owner_account_id,
            lgc_job_tracking.owner_account_full_name,
            lgc_job_tracking.row_height,
            lgc_job_tracking.step_done_count,
            lgc_job_tracking.last_step_name
          ORDER BY lgc_job_tracking.created_time DESC
          ${MAX_RETURN(sqlParams)}""";
      return query;
    }
  }

  static public class SearchJobTrackingIssue extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      int maxReturn        = sqlParams.getInteger('maxReturn', 1000);
      String query = """
        SELECT *,
          CASE WHEN status = 'NEW'        THEN 1
               WHEN status = 'DISCUSSION' THEN 2
               WHEN status = 'CONFIRMED'  THEN 3
               ELSE                            4 END AS status_index
        FROM lgc_job_tracking_issue issue 
        WHERE
          ${FILTER_BY_STORAGE_STATE(sqlParams)}
          ${AND_FILTER_BY_PARAM('company_id', sqlParams)}
          ${AND_SEARCH_BY_PARAMS(['label', 'label2', 'label3'], 'search', sqlParams)}
          ${AND_FILTER_BY_PARAM('resolver_account_id', 'accountId', sqlParams)}
          ${AND_FILTER_BY_PARAM('ref_entity_id', sqlParams)}
          ${AND_FILTER_BY_PARAM('ref_entity', sqlParams)}
          ${AND_FILTER_BY_PARAM('job_tracking_project_id', sqlParams)}
          ${AND_FILTER_BY_OPTION('status', 'status', sqlParams)}
         ORDER BY status_index, label, created_time DESC
         LIMIT ${maxReturn}
        """
      return query;
    }
  }

  public class SearchJobTrackingCells extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
        SELECT 
          cell."id"                      AS id,
          cell."status"                  AS status,
          cell."internal_issue_count"    AS internal_issue_count,
          cell."external_issue_count"    AS external_issue_count,
          cell."job_tracking_id"         AS job_tracking_id,
          cell."job_tracking_project_id" AS job_tracking_project_id,
          cell."job_tracking_column_id"  AS job_tracking_column_id,
          cell."start_time"              AS start_time,
          cell."end_time"                AS end_time,
          cell."string_value"            AS string_value,
          cell."double_value"            AS double_value,
          cell."boolean_value"           AS boolean_value,
          cell."date_value"              AS date_value,
          cell."version"                 AS version,
          col."name"                     AS name,
          col."data_type"                AS data_type,
          col."data_input_type"          AS data_input_type
        FROM lgc_job_tracking_cell cell
        JOIN lgc_job_tracking_column col ON col.id = cell.job_tracking_column_id
        WHERE 1=1
          ${AND_FILTER_BY_PARAM('cell.job_tracking_id', 'jobTrackingIds', sqlParams)}
          ${AND_FILTER_BY_PARAM('col.name', 'accessColumns', sqlParams)}
          ${AND_FILTER_BY_PARAM('col.data_type', 'dataType', sqlParams)}
          ${AND_FILTER_BY_PARAM('col.name', 'colunmName', sqlParams)}
      """;
      return query;
    }
  }
  
  public class SelectJobTrackingColumnByProjectId extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      int maxReturn = sqlParams.getInteger('maxReturn', 1000);
      
      String query = """
          SELECT *
          FROM lgc_job_tracking_column tracking_column
          WHERE 
            ${FILTER_BY_PARAM('company_id', sqlParams)}
            ${FILTER_BY_PARAM('job_tracking_project_id' ,'jobTrackingProjectId', sqlParams)}
          LIMIT ${maxReturn}
      """;
      return query;
    }
  }
  
  public class JobTrackingReport extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      int maxReturn = sqlParams.getInteger('maxReturn', 1000);
      
      String query = """
        SELECT
          job.owner_account_id,
          UPPER(MAX(job.owner_account_full_name))  AS owner_account_full_name,
          COUNT(cell.id)                           AS done_count
        FROM lgc_job_tracking_cell cell
        LEFT JOIN lgc_job_tracking_column col ON col.id = cell.job_tracking_column_id
        LEFT JOIN lgc_job_tracking job ON job.id = cell.job_tracking_id 
        WHERE 1=1
          AND col.data_type = 'Status'
          ${AND_FILTER_BY_PARAM('cell.job_tracking_project_id', 'jobTrackingProjectId', sqlParams)}
          ${AND_FILTER_BY_PARAM('job.owner_account_id', 'ownerAccountId', sqlParams)}
          ${AND_FILTER_BY_PARAM('cell.status', 'status', sqlParams)}
          ${AND_FILTER_BY_RANGE('cell.end_time', 'endTime', sqlParams)}
        GROUP BY job.owner_account_id
        ORDER BY done_count DESC
      """;
      return query;
    }
  }
  
  public class JobTrackingFCLImportReport extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      
      String query = """
            WITH done_count_cte AS (
                SELECT
                    job.owner_account_id,
                    UPPER(job.owner_account_full_name) AS owner_account_full_name,
                    COUNT(cell.id) AS done_count,
                    0 AS agent_price_checking,
                    0 AS agent_feed_back
                FROM lgc_job_tracking_cell cell
                LEFT JOIN lgc_job_tracking_column col ON col.id = cell.job_tracking_column_id
                LEFT JOIN lgc_job_tracking job ON job.id = cell.job_tracking_id
                WHERE job.owner_account_id IS NOT NULL
                    ${AND_FILTER_BY_PARAM('cell.job_tracking_project_id', 'jobTrackingProjectId', sqlParams)}
                    AND col.data_type = 'Status'
                    ${AND_FILTER_BY_PARAM('cell.status', 'status', sqlParams)}
                    ${AND_FILTER_BY_RANGE('cell.end_time', 'endTime', sqlParams)}
                GROUP BY job.owner_account_id, job.owner_account_full_name
            ),
            price_feedback_cte AS (
                SELECT
                    job.owner_account_id,
                    UPPER(job.owner_account_full_name) AS owner_account_full_name,
                    0 AS done_count,
                    SUM(
                        CASE 
                            WHEN col.name = 'agentPriceChecking' 
                            AND EXISTS (
                                SELECT 1
                                FROM lgc_job_tracking_cell key_cell
                                JOIN lgc_job_tracking_column key_col ON key_col.id = key_cell.job_tracking_column_id
                                WHERE key_cell.job_tracking_id = cell.job_tracking_id
                                    AND key_col.name = 'agent-key-date'
                                    ${AND_FILTER_BY_RANGE('key_cell.date_value', 'endTime', sqlParams)}
                            )
                            THEN COALESCE(cell.double_value, 0) 
                            ELSE 0 
                        END
                    ) AS agent_price_checking,
                    SUM(
                        CASE 
                            WHEN col.name = 'agentFeedback' 
                            AND EXISTS (
                                SELECT 1
                                FROM lgc_job_tracking_cell key_cell
                                JOIN lgc_job_tracking_column key_col ON key_col.id = key_cell.job_tracking_column_id
                                WHERE key_cell.job_tracking_id = cell.job_tracking_id
                                    AND key_col.name = 'agent-key-date'
                                    ${AND_FILTER_BY_RANGE('key_cell.date_value', 'endTime', sqlParams)}
                            )
                            THEN COALESCE(cell.double_value, 0) 
                            ELSE 0 
                        END
                    ) AS agent_feed_back
                FROM lgc_job_tracking_cell cell
                LEFT JOIN lgc_job_tracking_column col ON col.id = cell.job_tracking_column_id
                LEFT JOIN lgc_job_tracking job ON job.id = cell.job_tracking_id
                WHERE job.owner_account_id IS NOT NULL
                  ${AND_FILTER_BY_PARAM('cell.job_tracking_project_id', 'jobTrackingProjectId', sqlParams)}
                  AND col.name IN ('agentPriceChecking', 'agentFeedback')
                GROUP BY job.owner_account_id, job.owner_account_full_name
            )
            SELECT
                owner_account_id,
                MAX(owner_account_full_name) AS owner_account_full_name,
                SUM(agent_price_checking) AS agent_price_checking,
                SUM(agent_feed_back) AS agent_feed_back,
                SUM(done_count) AS done_count,
                (SUM(done_count) + SUM(agent_price_checking) + SUM(agent_feed_back)) AS total
            FROM (
                SELECT * FROM done_count_cte
                UNION ALL
                SELECT * FROM price_feedback_cte
            ) combined
            WHERE EXISTS (
              SELECT 1
              FROM lgc_job_tracking_project_permission per
              WHERE per.user_id = combined.owner_account_id
                ${AND_FILTER_BY_PARAM('per.job_tracking_project_id', 'jobTrackingProjectId', sqlParams)}
            )
            GROUP BY owner_account_id
            ORDER BY total DESC
          """;
      return query;
    }
  }

  public JobTrackingSql() {
    register(new SearchJobTracking());
    register(new SearchJobTrackingIssue());
    register(new SearchJobTrackingCells());
    register(new JobTrackingReport());
    register(new JobTrackingFCLImportReport());
  }
}