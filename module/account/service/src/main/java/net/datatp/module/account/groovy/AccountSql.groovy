package net.datatp.module.account.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder

class AccountSql extends Executor {
  public class SearchDeactivateEmployeeAccounts extends ExecutableSqlBuilder {
    @Override
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      String query = """
          WITH active_accounts as (
            SELECT 
              DISTINCT sat.account_id                      as account_id
            FROM security_access_token sat 
            WHERE sat.account_id IS NOT NULL
              AND created_time >= '01/01/2024'
              AND access_type = 'Employee'
          )
          SELECT 
            DISTINCT aa.id                  as account_id, 
            LOWER(aa.login_id)              as lower_login_id, 
            aa.login_id                     as login_id, 
            emp."label"                     as label, 
            emp.storage_state               as storage_state,
            emp.created_by                  as created_by,
            emp.created_time                as created_time,
            com.code                        as company_code
          FROM company_hr_employee emp 
            JOIN account_account aa ON aa.id = emp.account_id 
            JOIN company_company com ON com.id = emp.company_id 
          WHERE emp.account_id NOT IN (SELECT a.account_id FROM active_accounts a) 
          ORDER BY com.code, LOWER(aa.login_id) 
      """;
      return query;
    }
  }
  public AccountSql() {
    register(new SearchDeactivateEmployeeAccounts());
  }
}