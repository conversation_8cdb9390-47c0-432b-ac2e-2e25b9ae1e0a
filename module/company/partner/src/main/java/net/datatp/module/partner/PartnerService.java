package net.datatp.module.partner;

import java.util.List;

import lombok.Getter;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.partner.entity.PartnerCompany;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Getter
@Service("PartnerService")
public class PartnerService extends BaseComponent {

  @Autowired
  private PartnerAccountLogic partnerAccountLogic;

  @Autowired
  private PartnerCompanyLogic partnerCompanyLogic;

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerAccounts(ClientContext client, SqlQueryParams sqlParams) {
    return partnerAccountLogic.searchPartnerAccounts(client, sqlParams);
  }

  @Transactional
  public boolean changeStorageState(ClientContext clientCtx, ChangeStorageStateRequest req) {
    return partnerAccountLogic.changeStorageState(clientCtx, req);
  }

  // -------------------------- Partner Company --------------------------
  @Transactional(readOnly = true)
  public PartnerCompany getPartnerCompany(ClientContext client, Long partnerId) {
    return partnerCompanyLogic.getPartnerCompany(client, partnerId);
  }

  @Transactional(readOnly = true)
  public PartnerCompany getByTaxCode(ClientContext client, String taxCode) {
    return partnerCompanyLogic.getByTaxCode(client, taxCode);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerCompany(ClientContext client, SqlQueryParams sqlParams) {
    return partnerCompanyLogic.searchPartnerCompany(client, sqlParams);
  }

  @Transactional
  public PartnerCompany savePartnerCompany(ClientContext client, PartnerCompany partnerCompany) {
    return partnerCompanyLogic.savePartnerCompany(client, partnerCompany);
  }

  @Transactional
  public boolean changePartnerCompanyStorageState(ClientContext clientCtx, ChangeStorageStateRequest req) {
    return partnerCompanyLogic.changeStorageState(clientCtx, req);
  }

  @Transactional
  public Boolean deletePartnerCompanies(ClientContext client, List<Long> ids) {
    return partnerCompanyLogic.deletePartnerCompanies(client, ids);
  }

}