package net.datatp.module.partner.repository;

import java.io.Serializable;
import java.util.List;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.partner.entity.PartnerCompany;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PartnerCompanyRepository extends JpaRepository<PartnerCompany, Serializable> {

  @Query("SELECT p FROM PartnerCompany p WHERE p.id = :id AND p.storageState = 'ACTIVE'")
  PartnerCompany getById(@Param("id") Long id);

  @Query("SELECT p FROM PartnerCompany p WHERE p.taxCode = :taxCode")
  PartnerCompany getByTaxCode(@Param("taxCode") String taxCode);

  @Query("SELECT p FROM PartnerCompany p WHERE p.id IN :ids")
  List<PartnerCompany> findByIds(@Param("ids") List<Long> ids);

  @Modifying
  @Query("UPDATE PartnerCompany p SET p.storageState = :storageState WHERE p.id IN :ids ")
  int updateStorageState(@Param("storageState") StorageState state, @Param("ids") List<Long> ids);

}