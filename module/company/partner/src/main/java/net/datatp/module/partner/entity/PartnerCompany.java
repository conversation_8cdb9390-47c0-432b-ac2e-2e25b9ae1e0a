package net.datatp.module.partner.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = PartnerCompany.TABLE_NAME,
  indexes = {
    @Index(
      name = PartnerCompany.TABLE_NAME + "_name_idx",
      columnList = "name"
    ),
  }
)
@Getter
@Setter
@NoArgsConstructor
public class PartnerCompany extends CompanyEntity {

  public static final String TABLE_NAME = "company_partner_company";
  public static final String SEQUENCE = "company:partner_company";

  @Column(name = "code")
  private String code;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_modified")
  private Date dateModified;

  @Column(name = "tax_code", unique = true)
  private String taxCode;

  @Column(name = "name", length = 1024)
  private String name;

  @Column(name = "full_name", length = 1024)
  private String fullName;

  @Column(length = 9 * 1024)
  private String address;

  @Column(name = "industry_code")
  private String industryCode;

  @Column(name = "industry_label")
  private String industryLabel;

  @Column(name = "country_id")
  private Long countryId;

  @Column(name = "country_label")
  private String countryLabel;

  @Column(name = "province_id")
  private Long provinceId;

  @Column(name = "province_label")
  private String provinceLabel;

  @Column(name = "kcn_code")
  private String kcnCode;

  @Column(name = "kcn_label")
  private String kcnLabel;

  @Column(name = "email")
  private String email;

  @Column(name = "phone")
  private String phone;

  @Column(name = "website")
  private String website;

  private String representative;

  @Column(name = "account_creator_id")
  private Long accountCreatorId;

  @Column(name = "account_creator_label")
  private String accountCreatorLabel;

  @Column(length = 1024 * 32)
  private String note;

  public void setName(String name) {
    if (StringUtil.isEmpty(name)) return;
    this.name = name.toUpperCase();
  }

}