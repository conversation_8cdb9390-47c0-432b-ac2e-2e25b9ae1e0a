package net.datatp.module.partner.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class PartnerCompanySql extends Executor {

    public class SearchPartnerCompany extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

//      String query = """
//           SELECT
//              p.*,
//              aa.login_id as login_id
//           FROM company_partner_company p
//             JOIN account_account  aa ON aa.id = p.account_id
//           WHERE
//            ${FILTER_BY_OPTION('p.storage_state',   'storageState', sqlParams, ['ACTIVE'])}
//            ${AND_SEARCH_BY_PARAMS(['p.label', 'p.name', 'aa.loginId'], 'search', sqlParams)}
//            ${AND_FILTER_BY_PARAM("p.company_id", "companyId", sqlParams)}
//          ${MAX_RETURN(sqlParams)}
//      """;
            String query = """
           SELECT
              p.*
           FROM company_partner_company p
           WHERE 
            ${FILTER_BY_OPTION('p.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
            ${AND_SEARCH_BY_PARAMS(['p.label', 'p.name'], 'search', sqlParams)}
            ${AND_FILTER_BY_PARAM("p.company_id", "companyId", sqlParams)}
          ${MAX_RETURN(sqlParams)}
      """;
            return query;
        }
    }

    public PartnerCompanySql() {
        register(new SearchPartnerCompany())
    }

}