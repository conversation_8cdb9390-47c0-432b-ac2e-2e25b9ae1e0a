package net.datatp.module.partner;

import java.util.Date;
import java.util.List;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.partner.entity.PartnerCompany;
import net.datatp.module.partner.repository.PartnerCompanyRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PartnerCompanyLogic extends DAOService {

  @Autowired
  private PartnerCompanyRepository partnerCompanyRepo;

  @Autowired
  private SeqService seqService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(PartnerCompany.SEQUENCE, 1);
  }

  public PartnerCompany savePartnerCompany(ClientContext clientContext, PartnerCompany partner) {
    final boolean isNew = partner.isNew();
    if (isNew || StringUtil.isEmpty(partner.getCode())) {
      String code = "PAR" + String.format("%05d", seqService.nextSequence(PartnerCompany.SEQUENCE));
      partner.setDateCreated(new Date());
      partner.setCode(code);
    }
    partner.set(clientContext, clientContext.getCompanyId());
    return partnerCompanyRepo.save(partner);
  }

  public PartnerCompany getPartnerCompany(ClientContext clientContext, Long id) {
    return partnerCompanyRepo.getById(id);
  }

  public PartnerCompany getByTaxCode(ClientContext clientContext, String taxCode) {
    return partnerCompanyRepo.getByTaxCode(taxCode);
  }

  public List<SqlMapRecord> searchPartnerCompany(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/partner/groovy/PartnerCompanySql.groovy";
    sqlParams.addParam("companyId", client.getCompanyId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerCompany", sqlParams);
  }

  public boolean changeStorageState(ClientContext clientCtx, ChangeStorageStateRequest req) {
    partnerCompanyRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public Boolean deletePartnerCompanies(ClientContext client, List<Long> ids) {
    List<PartnerCompany> partnerCompanies = partnerCompanyRepo.findByIds(ids);
    partnerCompanyRepo.deleteAll(partnerCompanies);
    return true;
  }

}