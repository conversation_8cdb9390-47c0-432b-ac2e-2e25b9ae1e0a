package cloud.datatp.fforwarder.sales.booking.entity;

import cloud.datatp.fforwarder.core.common.ChargeType;
import cloud.datatp.fforwarder.core.common.FreightTerm;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.util.text.DateUtil;

@Entity
@Table(
    name = Booking.TABLE_NAME,
    indexes = {
        @Index(name = Booking.TABLE_NAME + "_company_id", columnList = "company_id"),
        @Index(name = Booking.TABLE_NAME + "_storage_state", columnList = "storage_state"),
        @Index(name = Booking.TABLE_NAME + "_inquiry_id_idx", columnList = "inquiry_id"),
    }
)
@DeleteGraphs({
    @DeleteGraph(target = BookingCustomClearance.class, joinField = "booking_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(target = BookingTruckTransportCharge.class, joinField = "booking_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(target = SpecificServiceInquiry.class, joinField = "inquiry_id"),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class Booking extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_sales_booking";

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "booking_date")
  private Date bookingDate;

  @Column(name = "booking_number")
  private String bookingNumber;

  @Column(name = "bfsone_reference")
  private String bfsoneReference;

  @Column(name = "hawb_no")
  private String hawbNo;

  @Column(name = "mawb_no")
  private String mawbNo;

  @Column(name = "shipment_type")
  private String shipmentType;


  /* ------------ Sender (Saleman) / Receiver (Cus/ Docs) -------------- */
  @Column(name = "receiver_account_id")
  private Long receiverAccountId;

  @Column(name = "receiver_label")
  private String receiverLabel;

  @Column(name = "receiver_bfsone_code")
  private String  receiverBFSOneCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_term")
  private FreightTerm paymentTerm = FreightTerm.PREPAID;

  @Column(name = "specific_quotation_charge_id")
  private Long sQuotationChargeId;

  @NotNull
  @Column(name = "inquiry_id")
  private Long inquiryId;

  @Enumerated(EnumType.STRING)
  @Column(name = "charge_type")
  private ChargeType chargeType = ChargeType.SEA;

  @Column(name = "charge_id")
  private Long chargeId;

  @Column(name = "note", length = 1024 * 32)
  private String note;

}