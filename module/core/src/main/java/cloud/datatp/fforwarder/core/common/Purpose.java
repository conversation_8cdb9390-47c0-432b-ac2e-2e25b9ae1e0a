package cloud.datatp.fforwarder.core.common;

import lombok.Getter;

@Getter
public enum Purpose {
  EXPORT("E", "Export"), IMPORT("I", "Import"), DOMESTIC("L", "Domestic");

  private final String abb;

  private final String label;

  Purpose(String abb, String label) {
    this.abb = abb;
    this.label = label;
  }

  public static Purpose parse(String token) {
    if(token == null || token.equals("NONE")) return DOMESTIC;
    return valueOf(token.toUpperCase());
  }
  

  static public boolean isExport(Purpose mode) {
    return EXPORT.equals(mode);
  }
  
  static public boolean isImport(Purpose mode) {
    return IMPORT.equals(mode);
  }
  
  static public boolean isDomestic(Purpose mode) {
    return DOMESTIC.equals(mode);
  }

  static public Purpose[] ALL = Purpose.values();
}