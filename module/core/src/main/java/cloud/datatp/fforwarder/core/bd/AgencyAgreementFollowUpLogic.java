package cloud.datatp.fforwarder.core.bd;

import cloud.datatp.fforwarder.core.bd.entity.AgencyAgreementFollowUp;
import cloud.datatp.fforwarder.core.bd.repository.AgencyAgreementFollowUpRepository;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AgencyAgreementFollowUpLogic extends CRMDaoService {
  
  @Autowired
  private AgencyAgreementFollowUpRepository repo;
  
  @Autowired
  private SecurityLogic securityLogic;
  
  public AgencyAgreementFollowUp getById(ClientContext client, Long id) {
    return repo.getById(id);
  }
  
  public AgencyAgreementFollowUp save(ClientContext client, AgencyAgreementFollowUp followUp) {
    followUp.set(client);
    
    if (followUp.getDateCreated() == null) followUp.setDateCreated(new Date());
    
    return repo.save(followUp);
  }
  
  public List<MapObject> saveRecords(ClientContext client, List<MapObject> records) {
    if (Collections.isEmpty(records)) return records;
    
    for (MapObject record : records) {
      
      AgencyAgreementFollowUp followUp = new AgencyAgreementFollowUp();
      Long id = record.getLong("id", null);
      if (id != null) followUp = getById(client, id);
      followUp = followUp.mergeFromMapObject(record);
      
      if (followUp.getDateCreated() == null) followUp.setDateCreated(new Date());
      
      AgencyAgreementFollowUp saved = save(client, followUp);
      record.put("id", saved.getId());
    }
    
    return records;
  }

  public List<SqlMapRecord> searchAgencyAgreementFollowUps(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "company-logistics-sales");
    if (permission == null) return new ArrayList<>();
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/BDSql.groovy";
      String scriptName = "SearchAgencyAgreementFollowUp";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(crmDataSource, sqlParams);
      return view.renameColumWithJavaConvention().getSqlMapRecords();
    } catch (Exception e) {
      log.error("Error when search Agency Agreement Follow Up", e);
      return new ArrayList<>();
    }
  }
  
  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  public boolean updateStorageState(ClientContext client, ChangeStorageStateRequest req) {
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}