package cloud.datatp.fforwarder.core.common;

public enum TypeOfService {
  AirExpTransactions,
  AirImpTransactions,
  CustomsLogistics,
  InlandTrucking,
  LogisticsCrossBorder,
  RoundUseTrucking,
  WarehouseService,
  SeaExpTransactions_FCL,
  SeaExpTransactions_LCL,
  SeaImpTransactions_FCL,
  SeaImpTransactions_LCL;
//  SeaExpTransactions_CSL
//  SeaImpTransactions_CSL

  static public TypeOfService parse(String token) {
    if(token == null) return InlandTrucking;
    return valueOf(token.toUpperCase());
  }
  
  TypeOfService getTypeOfService(TransportationMode mode, Purpose purpose) {
    if (TransportationMode.isAirTransport(mode) && Purpose.isExport(purpose)) return AirExpTransactions;
    if (TransportationMode.isAirTransport(mode) && Purpose.isImport(purpose)) return AirImpTransactions;
    
    if (TransportationMode.isSeaFCLTransport(mode) && Purpose.isExport(purpose)) return SeaExpTransactions_FCL;
    if (TransportationMode.isSeaFCLTransport(mode) && Purpose.isImport(purpose)) return SeaImpTransactions_FCL;
    
    if (TransportationMode.isSeaLCLTransport(mode) && Purpose.isExport(purpose)) return SeaExpTransactions_LCL;
    if (TransportationMode.isSeaLCLTransport(mode) && Purpose.isImport(purpose)) return SeaImpTransactions_LCL;
    
    if (TransportationMode.isTruckTransport(mode) && Purpose.isDomestic(purpose)) return InlandTrucking;
    if (TransportationMode.isTruckTransport(mode) && !Purpose.isDomestic(purpose)) return LogisticsCrossBorder;
    
    return InlandTrucking;
  }

  static public TypeOfService[] ALL = TypeOfService.values();

}