package cloud.datatp.fforwarder.core.bd;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.core.bd.entity.AgencyAgreementFollowUp;
import cloud.datatp.fforwarder.core.bd.entity.AnnualConference;
import cloud.datatp.fforwarder.core.bd.entity.NetworkMembershipFee;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("BDService")
public class BDService extends BaseComponent {

  @Autowired
  private AgencyAgreementFollowUpLogic agencyAgreementFollowUpLogic;
  
  @Autowired
  private AnnualConferenceLogic annualConferenceLogic;
  
  @Autowired
  private NetworkMembershipFeeLogic networkMembershipFeeLogic;

  //AgencyAgreementFollowUp

  @Transactional(readOnly = true)
  public AgencyAgreementFollowUp getAgencyAgreementFollowUpById(ClientContext client, Long id) {
    return agencyAgreementFollowUpLogic.getById(client, id);
  }

  @Transactional
  public AgencyAgreementFollowUp saveAgencyAgreementFollowUp(ClientContext client, AgencyAgreementFollowUp followUp) {
    return agencyAgreementFollowUpLogic.save(client, followUp);
  }

  @Transactional
  public List<MapObject> saveAgencyAgreementFollowUpRecords(ClientContext client, List<MapObject> records) {
    return agencyAgreementFollowUpLogic.saveRecords(client, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchAgencyAgreementFollowUps(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return agencyAgreementFollowUpLogic.searchAgencyAgreementFollowUps(client, company, sqlParams);
  }
  
  @Transactional
  public boolean deleteAgencyAgreementFollowUpByIds(ClientContext client, List<Long> ids) {
    return agencyAgreementFollowUpLogic.deleteByIds(client, ids);
  }
  
  @Transactional
  public boolean updateAgencyAgreementFollowUpStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return agencyAgreementFollowUpLogic.updateStorageState(client, req);
  }
  
  //AnnualConference
  @Transactional(readOnly = true)
  public AnnualConference getAnnualConferenceById(ClientContext client, Long id) {
    return annualConferenceLogic.getById(client, id);
  }

  @Transactional
  public AnnualConference saveAnnualConference(ClientContext client, AnnualConference annualConference) {
    return annualConferenceLogic.save(client, annualConference);
  }

  @Transactional
  public List<MapObject> saveAnnualConferenceRecords(ClientContext client, List<MapObject> records) {
    return annualConferenceLogic.saveRecords(client, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchAnnualConferences(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return annualConferenceLogic.searchAnnualConferences(client, company, sqlParams);
  }
  
  @Transactional
  public boolean deleteAnnualConferenceByIds(ClientContext client, List<Long> ids) {
    return annualConferenceLogic.deleteByIds(client, ids);
  }
  
  @Transactional
  public boolean updateAnnualConferenceStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return annualConferenceLogic.updateStorageState(client, req);
  }
  
  //NetworkMembershipFee
  @Transactional(readOnly = true)
  public NetworkMembershipFee getNetworkMembershipFeeById(ClientContext client, Long id) {
    return networkMembershipFeeLogic.getById(client, id);
  }

  @Transactional
  public NetworkMembershipFee saveNetworkMembershipFee(ClientContext client, NetworkMembershipFee fee) {
    return networkMembershipFeeLogic.save(client, fee);
  }

  @Transactional
  public List<MapObject> saveNetworkMembershipFeeRecords(ClientContext client, List<MapObject> records) {
    return networkMembershipFeeLogic.saveRecords(client, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchNetworkMembershipFees(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return networkMembershipFeeLogic.searchNetworkMembershipFees(client, company, sqlParams);
  }
  
  @Transactional
  public boolean deleteNetworkMembershipFeeByIds(ClientContext client, List<Long> ids) {
    return networkMembershipFeeLogic.deleteByIds(client, ids);
  }
  
  @Transactional
  public boolean updateNetworkMembershipFeeStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return networkMembershipFeeLogic.updateStorageState(client, req);
  }
}
