package cloud.datatp.fforwarder.core.partner.entity;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.partner.PartnerMonitorMessagePlugin;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = CRMPartner.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CRMPartner.TABLE_NAME + "_account_id",
      columnNames = {"account_id"}
    ),
  },
  indexes = {
    @Index(
      name = CRMPartner.TABLE_NAME + "_name_idx",
      columnList = "name"
    ),
    @Index(
      name = CRMPartner.TABLE_NAME + "_bfsone_partner_code_idx",
      columnList = "bfsone_partner_code"
    ),
    @Index(
      name = CRMPartner.TABLE_NAME + "_tax_code_idx",
      columnList = "tax_code"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class CRMPartner extends PersistableEntity<Long> {

  final static public String TABLE_NAME = "lgc_forwarder_crm_partner";

  // Core identification
  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  //TODO: An - rename to partner_code
  @Column(name = "bfsone_partner_code")
  private String bfsonePartnerCode;

  @Column(name = "partner_code_temp")
  private String partnerCodeTemp;

  @Column(name = "lead_code")
  private String leadCode;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_modified")
  private Date dateModified;

  @Enumerated(EnumType.STRING)
  @Column(name = "partner_group", nullable = false)
  private BFSOnePartnerGroup partnerGroup;

  @Enumerated(EnumType.STRING)
  @Column(name = "category")
  private Category category = Category.CUSTOMER;

  // ----------- Basic information ---------------
  @Column(name = "name", length = 1024)
  private String name;

  @Column(name = "label", length = 2 * 1024)
  private String label;

  @Column(name = "localized_label", length = 2 * 1024)
  private String localizedLabel;

  // ----------------- Contact information ----------------
  @Column(name = "personal_contact")
  private String personalContact;

  @Column(name = "email")
  private String email;

  @Column(name = "fax")
  private String fax;

  @Column(name = "cell")
  private String cell;

  @Column(name = "home_phone")
  private String homePhone;

  @Column(name = "work_phone")
  private String workPhone;

  // ----------------  Business information ----------------
  @Column(name = "tax_code")
  private String taxCode;

  @Column(name = "source")
  private String source;

  @Column(name = "investment_origin")
  private String investmentOrigin;

  @Column(name = "industry_code")
  private String industryCode;

  @Column(name = "industry_label")
  private String industryLabel;

  @Column(name = "routing", length = 1024)
  private String routing;

  // ----------------  Location information ----------------
  @Column(name = "country_id")
  private Long countryId;

  @Column(name = "country_label")
  private String countryLabel;

  @Column(name = "province_id")
  private Long provinceId;

  @Column(name = "province_label")
  private String provinceLabel;

  @Column(name = "kcn_code")
  private String kcnCode;

  @Column(name = "kcn_label")
  private String kcnLabel;

  @Column(length = 9 * 1024)
  private String address;

  @Column(name = "localized_address", length = 9 * 1024)
  private String localizedAddress;

  // ----------------  Bank information ----------------
  @Column(name = "bank_accs_no")
  private String bankAccsNo;

  @Column(name = "bank_name", length = 64 * 1024)
  private String bankName;

  @Column(name = "bank_address", length = 64 * 1024)
  private String bankAddress;

  @Column(name = "swift_code")
  private String swiftCode;

  @Column(name = "print_custom_confirm_bill_info", length = 64 * 1024)
  private String printCustomConfirmBillInfo;

  @Column(length = 1024 * 32)
  private String note;

  // Sales information
  @Column(name = "sale_owner_contact_code")
  private String saleOwnerContactCode;

  @Column(name = "sale_owner_username")
  private String saleOwnerUsername;

  @Column(name = "input_username")
  private String inputUsername;

  @Enumerated(EnumType.STRING)
  private ShareableScope shareable;

  @Enumerated(EnumType.STRING)
  private Scope scope = Scope.Domestic;

  @Column(name = "bfsone_group_id")
  private Integer bfsoneGroupId;

  @Column(name = "group_name")
  private String groupName;

  @Column(name = "is_refund")
  private boolean refund = false;

  @Transient
  private String salemanObligationCode;

  // ----------------  Saleman Request ----------------
  @Transient
  private Long requestSalemanAccountId;

  @Transient
  private String requestSalemanLabel;

  public void setPartnerCodeTemp(String partnerCodeTemp) {
    this.partnerCodeTemp = partnerCodeTemp;
    if (StringUtil.isEmpty(bfsonePartnerCode)) bfsonePartnerCode = partnerCodeTemp;
  }

  public CRMPartner(MapObject record) {
    this.bfsonePartnerCode = record.getString("partner_code");
    this.name = record.getString("name", "");
    this.label = record.getString("label", "");
    this.localizedLabel = record.getString("localized_label", "");
    this.address = record.getString("address", "");
    this.localizedAddress = record.getString("localized_address", "");
    this.personalContact = record.getString("personal_contact", "");
    this.email = record.getString("email", "");
    this.fax = record.getString("fax", "");
    this.cell = record.getString("cell", "");
    this.homePhone = record.getString("home_phone", "");
    this.workPhone = record.getString("work_phone", "");
    this.taxCode = record.getString("tax_code", null);
    this.countryLabel = record.getString("country_label", "");
    this.source = record.getString("source", "");
    this.bankAccsNo = record.getString("bank_accs_no", "");
    this.bankName = record.getString("bank_name", "");
    this.bankAddress = record.getString("bank_address", "");
    this.scope = Scope.parse(record.getString("scope", ""));
    this.industryCode = record.getString("industry_code");
    this.industryLabel = record.getString("industry_label");
    this.note = record.getString("note_1", "");
    this.category = Category.parse(record.getString("category", ""));

    this.saleOwnerContactCode = record.getString("sale_owner_code", "");
    this.saleOwnerUsername = record.getString("sale_owner_username", "");

    this.inputUsername = record.getString("input_username", "");
    this.salemanObligationCode = record.getString("saleman_obligation_code", "");

    this.dateCreated = record.getDate("date_created", null);
    this.dateModified = record.getDate("date_modified", null);

    String additionalNote = record.getString("note_2", "");
    if (!additionalNote.isEmpty()) this.note += "\n" + additionalNote;

    String customerTypeIdStr = record.getString("customer_type_id", null);
    if (StringUtil.isNotEmpty(customerTypeIdStr)) {
      int customerTypeId = Integer.parseInt(customerTypeIdStr);
      if (customerTypeId > 0) {
      }
      this.bfsoneGroupId = customerTypeId;

      if (customerTypeId == 1) {
        this.groupName = "NORMAL";
      } else if (customerTypeId == 2) {
        this.groupName = "FACTORY";
      } else if (customerTypeId == 3) {
        this.groupName = "CO-LOADER";
      } else if (customerTypeId == 4) {
        this.groupName = "OTHERS";
      }
    }

    withBillReference();
    computePartnerGroup();
    boolean isPublic = record.getBoolean("public", false);
    computeShareableScope(isPublic);
  }

  public void setName(String name) {
    if (StringUtil.isEmpty(name)) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Name is empty!!!");
    }
    this.name = name.toUpperCase();
  }

  public CRMPartner computeShareableScope(boolean isPublic) {
    if (isPublic) shareable = ShareableScope.ORGANIZATION;
    else shareable = ShareableScope.PRIVATE;
    return this;
  }

  public CRMPartner withInputEmployee(CrmUserRole inputPerson) {
    this.inputUsername = inputPerson.getBfsoneUsername();
    return this;
  }

  public CRMPartner withAccount(Account account) {
    accountId = account.getId();
    if (StringUtil.isEmpty(bfsonePartnerCode)) bfsonePartnerCode = account.getLegacyLoginId();
    return this;
  }

  public void computePartnerGroup() {
    if (category != null) this.partnerGroup = category.getGroup();
  }

  public MapObject toBFSOnePartner() {
    computePartnerGroup();
    MapObject rec = new MapObject();
    if (StringUtil.isNotEmpty(this.partnerCodeTemp)) {
      rec.put("PartnerID", this.partnerCodeTemp);
    } else {
      rec.put("PartnerID", this.bfsonePartnerCode);
    }
    rec.put("PartnerName", this.name);
    rec.put("PartnerName2", this.label);
    rec.put("PartnerName3", this.localizedLabel);
    rec.put("PersonalContact", this.personalContact);
    if (StringUtil.isNotEmpty(this.address)) {
      if (this.address.trim().isEmpty()) this.address = "N/A";
    } else {
      this.address = "N/A";
    }
    rec.put("Address", this.address);

    if (StringUtil.isNotEmpty(this.localizedAddress)) {
      if (this.localizedAddress.trim().isEmpty()) this.localizedAddress = this.address;
    } else {
      this.localizedAddress = this.address;
    }
    rec.put("Address2", this.localizedAddress);
    rec.put("Fax", this.fax);
    rec.put("Cell", this.cell);
    rec.put("Taxcode", this.taxCode);
    rec.put("Email", this.email);
    rec.put("Country", this.countryLabel != null ? this.countryLabel : "VIETNAM");
    rec.put("Website", "");
    rec.put("SalemanID", "");
    rec.put("Source", this.source);
    rec.put("BankAccsNo", this.bankAccsNo);
    rec.put("BankName", this.bankName);
    rec.put("BankAddress", this.bankAddress);
    rec.put("SwiftCode", this.swiftCode);
    rec.put("Location", this.scope.toString());
    rec.put("Category", this.category.getName());
    rec.put("IndustryID", this.industryCode);
    rec.put("IndustryLabel", this.industryLabel);
    rec.put("DateToEstablish", null);
    rec.put("Notes", this.note);
    rec.put("RequestUser", "");
    rec.put("Email_Request", "");
    rec.put("isRefund", this.isRefund());

    if (this.partnerGroup.equals(BFSOnePartnerGroup.CUSTOMERS)) {
      if (Objects.nonNull(this.bfsoneGroupId)) {
        rec.put("PartnerGroupID", this.bfsoneGroupId);
      } else if (this.groupName.equalsIgnoreCase("NORMAL")) {
        rec.put("PartnerGroupID", 1.0);
      } else if (this.groupName.equalsIgnoreCase("FACTORY")) {
        rec.put("PartnerGroupID", 2.0);
      } else if (this.groupName.equalsIgnoreCase("CO-LOADER")) {
        rec.put("PartnerGroupID", 3.0);
      } else if (this.groupName.equalsIgnoreCase("OTHERS")) {
        rec.put("PartnerGroupID", 4.0);
      } else {
        throw RuntimeError.IllegalArgument("Partner group is not supported: " + this.groupName);
      }
    } else {
      rec.put("PartnerGroupID", "");
    }
    return rec;
  }

  public CRMPartner withBillReference() {
    if (StringUtil.isNotEmpty(this.printCustomConfirmBillInfo)) return this;
    StringJoiner joiner = new StringJoiner("\n");
    joiner.setEmptyValue("");
    joiner.add(name != null ? name : "");
    joiner.add(address != null ? address.trim() : "");
    joiner.add(personalContact != null ? personalContact.trim() : "");

    StringBuilder contact = new StringBuilder();
    if (StringUtil.isNotEmpty(cell)) contact.append("TEL :").append(cell.trim());
    if (StringUtil.isNotEmpty(fax)) {
      if (!contact.isEmpty()) contact.append(" ");
      contact.append("Fax:").append(fax.trim());
    }

    joiner.add(contact.toString());
    this.setPrintCustomConfirmBillInfo(joiner.toString());
    return this;
  }

  public static Map<String, List<CRMPartner>> groupBySaleObligation(List<? extends MapObject> records) {
    return records.stream().map(CRMPartner::new)
      .collect(Collectors.groupingBy(CRMPartner::getSalemanObligationCode));
  }

  public static Map<String, List<CRMPartner>> groupBySaleOwnerObligation(List<? extends MapObject> records) {
    return records.stream().map(CRMPartner::new)
      .collect(Collectors.groupingBy(CRMPartner::getSaleOwnerContactCode));
  }

  public static List<CRMPartner> computeToBFSOnePartner(List<MapObject> records) {
    return records.stream().map(CRMPartner::new).collect(Collectors.toList());
  }

  public static CRMMessageSystem toApprovePartnerZaloMessage(String content) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(content);
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.ZALO);
    message.setReferenceId(null);
    message.setReferenceType(CRMPartner.TABLE_NAME);
    message.setPluginName(PartnerMonitorMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList("84842283596")));
    return message;
  }

}