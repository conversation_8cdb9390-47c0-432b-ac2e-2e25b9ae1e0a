package net.datatp.module.core.security.groovy

import net.datatp.module.data.db.ExecutableSqlBuilder

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.util.ds.MapObject

public class SecuritySql extends Executor {
  public class SelectAccessToken extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          at.*,
          aa.full_name   AS user_full_name
        FROM security_access_token at
        LEFT JOIN account_account aa ON aa.id = at.account_id
        WHERE
          ${FILTER_BY_STORAGE_STATE('at', sqlParams)}
          ${AND_FILTER_BY_PARAM('at.account_id', 'accountId', sqlParams)}
          ${AND_SEARCH_BY_PARAMS(['at.label', 'aa.login_id'], 'search', sqlParams )}
        ${ORDER_BY(sqlParams)}
        ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }

  public class SearchUserAppPermissions extends ExecutableSqlBuilder{
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          ap.*,
          sa.module      AS module,
          sa.name        AS name,
          aa.login_id    AS login_id,
          aa.full_name   AS user_full_name
        FROM security_app_permission ap
          INNER JOIN security_app sa ON sa.id = ap.app_id
          INNER JOIN account_account aa ON aa.id = ap.account_id
        WHERE
          ${FILTER_BY_STORAGE_STATE('ap', sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.app_id', 'appId', sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.account_id', 'accountId',sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.access_type', 'accessType',sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.company_id', 'companyId',sqlParams)}
          ${AND_FILTER_BY_RANGE('ap.created_time', 'createdTime', sqlParams)}
          ${AND_FILTER_BY_RANGE('ap.modified_time', 'modifiedTime', sqlParams)}
          ${AND_FILTER_BY_OPTION('ap.capability', "capability", sqlParams)}
          ${ORDER_BY(sqlParams)}
        ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }

  public class SearchPermissions extends ExecutableSqlBuilder{
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          sa.module      AS app_module,
          sa.name        AS app_name,
          acc.full_name  AS user_full_name,
          acc.login_id,
          ap.*
        FROM security_app_permission ap
          INNER JOIN security_app sa ON sa.id = ap.app_id
          LEFT JOIN account_account acc ON acc.id = ap.account_id
        WHERE
          ${FILTER_BY_STORAGE_STATE('ap', sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.app_id', 'appId', sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.account_id', 'accountId',sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.access_type', 'accessType',sqlParams)}
          ${AND_FILTER_BY_PARAM('ap.company_id', 'companyId',sqlParams)}
          ${AND_FILTER_BY_RANGE('ap.created_time', 'createdTime', sqlParams)}
          ${AND_FILTER_BY_RANGE('ap.modified_time', 'modifiedTime', sqlParams)}
          ${AND_FILTER_BY_OPTION('ap.capability', "capability", sqlParams)}
          ${ORDER_BY(sqlParams)}
        ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }

  public SecuritySql(){
    register(new SelectAccessToken())
    register(new SearchUserAppPermissions())
    register(new SearchPermissions())
  }
}