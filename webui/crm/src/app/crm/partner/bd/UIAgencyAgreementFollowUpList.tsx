import React, { RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { BBRefBFSOnePartner } from '..';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';
import { T } from 'app/crm/price';
import { AgencyAgreementFollowUpStatusUtils } from 'app/crm/sales';

const SESSION = app.host.DATATP_SESSION;

export class UIAgencyAgreementFollowUpEditor extends entity.DbEntityEditor {

  onSave = () => {
    let { appContext, observer, onPostCommit } = this.props;
    let followUp = observer.getMutableBean();

    const missingAgentInfo = !followUp['agentName'] || !followUp['agentCode'];
    if (missingAgentInfo) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the agent before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    appContext.createHttpBackendCall('BDService', 'saveAgencyAgreementFollowUp', { followUp: followUp })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Save Success`);
        observer.replaceWith(data);
        if (onPostCommit) {
          onPostCommit(followUp);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
  }

  handleNotificationChange = (bean: any, _field: string, _oldVal: any, newVal: boolean) => {
    if (!newVal) {
      bean.notificationTime = null;
    } else if (newVal) {
      bean.notificationTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    this.forceUpdate();
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    let followUp = observer.getMutableBean();
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-1 p-1'>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBDateInputMask field='dateCreated' label={T('Date Created')} bean={followUp} format='DD/MM/YYYY' disabled />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBDateInputMask field='signedDate' label={T('Signed Date')} bean={followUp} format='DD/MM/YYYY' />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField field='status' label={T('Status')} bean={followUp}
                options={['ON_PROCESS', 'FINISH', 'PENDING']} optionLabels={['On Process', 'Finish', 'Pending']} />
            </bs.Col>
            <bs.Col span={3}>
              <BBRefCrmUserRole appContext={appContext} pageContext={pageContext} placeholder='Handled By' label='Handled By'
                bean={followUp} beanIdField={'handledByAccountId'} beanLabelField={'handledByLabel'} />
            </bs.Col>
          </bs.Row>

          <BBRefBFSOnePartner placeholder='Agent' label='Agent'
            style={{ width: '100%' }} placement='auto-start'
            appContext={appContext} pageContext={pageContext} minWidth={400} hideMoreInfo
            bean={followUp} beanIdField={'agentCode'} beanLabelField={'agentName'} partnerType='Agent'
            onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
              bean['agentCode'] = selectOpt['bfsonePartnerCode'];
              bean['agentName'] = selectOpt['name'];
              this.forceUpdate();
            }}
          />

          <bs.Row>
            <bs.Col span={6}>
              <module.settings.BBRefCountry appContext={appContext} pageContext={pageContext}
                placeholder='Country' label='Country'
                bean={followUp} beanIdField={'countryId'} beanLabelField={'countryLabel'} refCountryBy='id'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['countryId'] = selectOpt['id'];
                  bean['countryLabel'] = selectOpt['label'];
                  this.forceUpdate();
                }}
              />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBNumberField field='creditAmount' label={T('Credit Amount')} bean={followUp} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBTextField field='address' label={T('Address')} bean={followUp} style={{ height: '6em' }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBTextField style={{ height: '6em' }} field='memberOfNetwork' label={T('Member Of Network')} bean={followUp} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBTextField style={{ height: '6em' }}
                field='formAgreementGivenBy' label={T('Form Agreement Given By')} bean={followUp} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBTextField field='subjectMail' label={T('Subject Mail')} bean={followUp} style={{ height: '6em' }} />
            </bs.Col>
          </bs.Row>

          <input.BBTextField field='note' label={T('Note')} bean={followUp} style={{ height: '6em' }} />

          <div className="d-flex align-items-center justify-content-end gap-4 p-2 border rounded bg-light mt-1"
            style={{ minHeight: 48 }}>
            {followUp.sendingEmail &&
              <div className="d-flex align-items-center gap-2 border-end pe-4">
                <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Notification Time')}:</span>
                <input.BBDateInputMask field='notificationTime' bean={followUp}
                  format={'DD/MM/YYYY'} timeFormat={true} />
              </div>
            }

            <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
              <input.BBCheckboxField field='sendingEmail' bean={followUp} value={false}
                onInputChange={this.handleNotificationChange} />
              <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Send Email')}</span>
            </label>
          </div>

        </div>
        <bs.Toolbar className='border' >
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Save} label={T('Save')}
            onClick={this.onSave}
          />
        </bs.Toolbar>
      </div >
    );
  }
}

export class UIAgencyAgreementFollowUpListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'BDService',
      searchMethod: 'searchAgencyAgreementFollowUps'
    }

    this.searchParams = {
      "params": { space: space },
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }


  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export interface UIAgencyAgreementFollowUpListProps extends entity.DbEntityListProps { }

export class UIAgencyAgreementFollowUpList extends entity.DbEntityList<UIAgencyAgreementFollowUpListProps> {

  createVGridConfig(): grid.VGridConfig {
    const CELL_HEIGHT: number = 70;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('agentCode', T('Agent Code'), 120),
          { name: 'agentName', label: T('Agent Name'), width: 300 },
          { name: 'address', label: T('Address'), width: 300 },
          { name: 'countryLabel', label: T('Country'), width: 150, filterable: true, filterableType: 'options' },
          {
            name: 'memberOfNetwork', label: T('Member Of Network'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          { name: 'creditAmount', label: T('Credit Amount'), width: 120 },
          {
            name: 'signedDate', label: T('Signed Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'formAgreementGivenBy', label: T('Form Agreement Given By'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          { name: 'handledByLabel', label: T('Handled By'), width: 250, filterable: true, filterableType: 'options' },
          {
            name: 'note', label: T('Note'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'subjectMail', label: T('Subject Mail'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'dateCreated', label: T('Date Created'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'sendingEmail', label: T('Sending Email'), width: 120, cssClass: 'd-flex justify-content-center align-items-center',
            filterable: true, filterableType: 'boolean',
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBCheckboxField value={false}
                  bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'notificationTime', label: T('Notification Time'), width: 200, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'status', label: T('Status'), width: 120, container: 'fixed-right',
            filterable: true, filterableType: 'options',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              let currentStatus = AgencyAgreementFollowUpStatusUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = AgencyAgreementFollowUpStatusUtils.getAgencyAgreementFollowUpStatusList();
              const statusRemaining = statusList.filter(status =>
                status.value !== record['status'] &&
                status.value !== 'IN_PROGRESS' &&
                status.value !== 'DONE'
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100" key={util.IDTracker.next()}
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => {
                              record['status'] = opt.value;
                              this.saveChanges(record)
                              let event: grid.VGridCellEvent = {
                                row: record.row,
                                field: _field,
                                event: 'Modified'
                              }
                              this.vgridContext.broadcastCellEvent(event);
                            }}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover >
              );
            }
          },
        ]
      },
      toolbar: { hide: true },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  saveChanges = (modified: any) => {
    let { appContext } = this.props;
    this.vgridContext.model.addOrUpdateByRecordId(modified);

    appContext.createHttpBackendCall('BDService', 'saveAgencyAgreementFollowUpRecords', { records: [modified] })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
        this.forceUpdate();
        this.onModifyBean(entity);
      })
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { appContext, pageContext } = this.props;
    let record = dRecord.record;
    appContext.createHttpBackendCall('BDService', 'getAgencyAgreementFollowUpById', { id: record.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIAgencyAgreementFollowUpEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
              onPostCommit={(entity: any) => {
                pageCtx.back();
                this.onModifyBean(entity);
              }} />
          );
        }
        pageContext.createPopupPage("edit-agency-agreement-follow-up", T("Agency Agreement Follow Up"), createAppPage, { size: 'flex-lg', backdrop: "static" });
      })
      .call();
  }

  onModifyBean = (bean: any, action?: entity.ModifyBeanActions) => {
    let { onModifyBean } = this.props;
    if (onModifyBean) {
      onModifyBean(bean, action);
    } else {
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }
}

interface UIAgencyAgreementFollowUpListPageProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System'
}
export class UIAgencyAgreementFollowUpListPage extends app.AppComponent<UIAgencyAgreementFollowUpListPageProps> {
  listRef: RefObject<UIAgencyAgreementFollowUpList>;
  viewId: number = util.IDTracker.next();
  plugin: UIAgencyAgreementFollowUpListPlugin;
  filter: any = { maxReturn: 500, pattern: '' };

  constructor(props: UIAgencyAgreementFollowUpListPageProps) {
    super(props);
    this.listRef = React.createRef();
    this.plugin = new UIAgencyAgreementFollowUpListPlugin(this.props.space);
  }

  componentDidMount(): void {
    if (this.listRef.current) this.forceUpdate();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  };

  onModifyBean = (_bean: any, _action?: entity.ModifyBeanActions) => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      if (this.listRef.current) {
        let uiList: UIAgencyAgreementFollowUpList = this.listRef.current;
        uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
        uiList.getVGridContext().model.filter();
        uiList.forceUpdate();
      }
    }
  }

  onAdd = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      let today: string = util.TimeUtil.javaCompactDateTimeFormat(new Date());
      let newBean = {
        dateCreated: today,
        signedDate: today,
        status: 'ON_PROCESS',
        handledByAccountId: SESSION.getAccountId(),
        handledByLabel: SESSION.getAccountAcl().getFullName(),
        notificationTime: today,
        sendingEmail: true
      }

      return (
        <UIAgencyAgreementFollowUpEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newBean)}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.viewId = util.IDTracker.next();
            this.forceUpdate();
          }} />
      );
    }
    pageContext.createPopupPage("new-agency-agreement-follow-up", T("New Agency Agreement Follow Up"), createAppPage, { size: 'flex-lg', backdrop: "static" });
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Agency Agreement Follow Up Selected!"));
      return;
    }

    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('BDService', 'deleteAgencyAgreementFollowUpByIds', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          this.viewId = util.IDTracker.next();
          this.forceUpdate();
        })
        .withEntityOpNotification('delete', 'Agency Agreement Follow Up')
        .call();
    };

    let messageEle = (<div className="text-danger">Do you want to delete these records?</div>);
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  render() {
    const { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className='flex-hbox'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
                options={[500, 1000, 2000, 5000]}
                optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
                onInputChange={this.onModify} />
            </div>

            <input.WStringInput className={'flex-hbox'} style={{ maxWidth: 300 }}
              name='search' value={this.filter.pattern}
              placeholder={('Enter Agent Name/Code or Subject Mail ......')} onChange={this.onChangePattern} />
          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onAdd} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap} >
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>

            <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen() || !moderatorCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>


            {
              this.listRef.current ?
                <XLSXButton appContext={appContext} pageContext={pageContext} context={this.listRef.current.getVGridContext()} />
                : null
            }
          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UIAgencyAgreementFollowUpList ref={this.listRef}
            appContext={appContext} pageContext={pageContext} plugin={this.plugin} onModifyBean={this.onModifyBean} />
        </div>
      </div>
    )
  }
}

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model["fileName"] = 'Agency Agreement Follow Up.xlsx'
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button laf={"info"} onClick={this.onExportCustomization} className="border-0 border-end rounded-0 p-1 mx-1"
        style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}
