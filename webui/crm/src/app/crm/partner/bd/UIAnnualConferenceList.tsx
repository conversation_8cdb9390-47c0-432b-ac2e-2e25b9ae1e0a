import React, { RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';

import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';
import { T } from 'app/crm/price';

const SESSION = app.host.DATATP_SESSION;

export class UIAnnualConferenceEditor extends entity.DbEntityEditor {

  onSave = () => {
    let { appContext, observer, onPostCommit } = this.props;
    let annualConference = observer.getMutableBean();

    const missingNetwork = !annualConference['network'];
    if (missingNetwork) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the network before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const missingEvent = !annualConference['event'];
    if (missingEvent) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the event before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    appContext.createHttpBackendCall('BDService', 'saveAnnualConference', { annualConference: annualConference })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Save Success`);
        observer.replaceWith(data);
        if (onPostCommit) {
          onPostCommit(annualConference);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
  }

  handleNotificationChange = (bean: any, _field: string, _oldVal: any, newVal: boolean) => {
    if (!newVal) {
      bean.notificationTime = null;
    } else if (newVal) {
      bean.notificationTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    this.forceUpdate();
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    let annualConference = observer.getMutableBean();
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-1 p-1'>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField field='dateCreated' label={T('Date Created')} bean={annualConference} timeFormat={false} disable />
            </bs.Col>
            <bs.Col span={6}>
              <BBRefCrmUserRole appContext={appContext} pageContext={pageContext} placeholder='Input By' label='Input By'
                bean={annualConference} beanIdField={'inputByAccountId'} beanLabelField={'inputByAccountLabel'} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField field='network' label={T('Network')} bean={annualConference} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField field='event' label={T('Event')} bean={annualConference} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField field='startDate' label={T('Start Date')} bean={annualConference} timeFormat={false} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBDateTimeField field='endDate' label={T('End Date')} bean={annualConference} timeFormat={false} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField field='venue' label={T('Venue')} bean={annualConference} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField field='delegatesFee' label={T('Delegates Fee')} bean={annualConference} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField field='registrationPeriod' label={T('Registration Period')} bean={annualConference} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBIntField field='expectedAttendance' label={T('Expected Attendance')} bean={annualConference} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField field='assignedDelegates' label={T('Assigned Delegates')} bean={annualConference} />
            </bs.Col>
          </bs.Row>

          <input.BBTextField field='note' label={T('Note')} bean={annualConference} style={{ height: '6em' }} />

          <div className="d-flex align-items-center justify-content-end gap-4 p-2 border rounded bg-light mt-1"
            style={{ minHeight: 48 }}>
            {annualConference.autoReminder &&
              <div className="d-flex align-items-center gap-2 border-end pe-4">
                <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Notification Time')}:</span>
                <input.BBDateInputMask field='notificationTime' bean={annualConference}
                  format={'DD/MM/YYYY'} timeFormat={true} />
              </div>
            }

            <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
              <input.BBCheckboxField field='autoReminder' bean={annualConference} value={false}
                onInputChange={this.handleNotificationChange} />
              <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Auto Reminder')}</span>
            </label>
          </div>

        </div>
        <bs.Toolbar className='border' >
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Save} label={T('Save')}
            onClick={this.onSave}
          />
        </bs.Toolbar>
      </div >
    );
  }
}

export class UIAnnualConferenceListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'BDService',
      searchMethod: 'searchAnnualConferences'
    }

    this.searchParams = {
      "params": { space: space },
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }


  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export interface UIAnnualConferenceListProps extends entity.DbEntityListProps { }

export class UIAnnualConferenceList extends entity.DbEntityList<UIAnnualConferenceListProps> {

  createVGridConfig(): grid.VGridConfig {
    const CELL_HEIGHT: number = 70;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('network', T('Network'), 300),
          {
            name: 'event', label: T('Event'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'startDate', label: T('Start Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'endDate', label: T('End Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          { name: 'venue', label: T('Venue'), width: 150 },
          { name: 'delegatesFee', label: T('Delegates Fee'), width: 150 },
          { name: 'registrationPeriod', label: T('Registration Period'), width: 160 },
          { name: 'expectedAttendance', label: T('Expected Attendance'), width: 160 },
          { name: 'assignedDelegates', label: T('Assigned Delegates'), width: 150 },
          {
            name: 'note', label: T('Note'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          { name: 'inputByAccountLabel', label: T('Input By'), width: 250, filterable: true, filterableType: 'options' },
          {
            name: 'dateCreated', label: T('Date Created'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'autoReminder', label: T('Auto Reminder'), width: 120, cssClass: 'd-flex justify-content-center align-items-center',
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBCheckboxField value={false}
                  bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'notificationTime', label: T('Notification Time'), width: 200, format: util.text.formater.dateTime,
            filterable: true, filterableType: 'date', sortable: true,
          },
        ]
      },
      toolbar: { hide: true },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { appContext, pageContext } = this.props;
    let record = dRecord.record;
    appContext.createHttpBackendCall('BDService', 'getAnnualConferenceById', { id: record.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIAnnualConferenceEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
              onPostCommit={(entity: any) => {
                pageCtx.back();
                this.onModifyBean(entity);
              }} />
          );
        }
        pageContext.createPopupPage("edit-annual-conference", T("Annual Conference"), createAppPage, { size: 'flex-lg', backdrop: "static" });
      })
      .call();
  }

  onModifyBean = (bean: any, action?: entity.ModifyBeanActions) => {
    let { onModifyBean } = this.props;
    if (onModifyBean) {
      onModifyBean(bean, action);
    } else {
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }
}

interface UIAnnualConferenceListPageProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System'
}
export class UIAnnualConferenceListPage extends app.AppComponent<UIAnnualConferenceListPageProps> {
  listRef: RefObject<UIAnnualConferenceList>;
  viewId: number = util.IDTracker.next();
  plugin: UIAnnualConferenceListPlugin;
  filter: any = { maxReturn: 500, pattern: '' };

  constructor(props: UIAnnualConferenceListPageProps) {
    super(props);
    this.listRef = React.createRef();
    this.plugin = new UIAnnualConferenceListPlugin(this.props.space);
  }

  componentDidMount(): void {
    if (this.listRef.current) this.forceUpdate();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  };

  onModifyBean = (_bean: any, _action?: entity.ModifyBeanActions) => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      if (this.listRef.current) {
        let uiList: UIAnnualConferenceList = this.listRef.current;
        uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
        uiList.getVGridContext().model.filter();
        uiList.forceUpdate();
      }
    }
  }

  onAdd = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      let today: string = util.TimeUtil.javaCompactDateTimeFormat(new Date());
      let newBean = {
        inputByAccountId: SESSION.getAccountId(),
        inputByAccountLabel: SESSION.getAccountAcl().getFullName(),
        notificationTime: today,
        dateCreated: today,
        autoReminder: true,
      }

      return (
        <UIAnnualConferenceEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newBean)}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.viewId = util.IDTracker.next();
            this.forceUpdate();
          }} />
      );
    }
    pageContext.createPopupPage("new-annual-conference", T("New Annual Conference"), createAppPage, { size: 'flex-lg', backdrop: "static" });
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Annual Conference Selected!"));
      return;
    }

    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('BDService', 'deleteAnnualConferenceByIds', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          this.viewId = util.IDTracker.next();
          this.forceUpdate();
        })
        .withEntityOpNotification('delete', 'Annual Conference')
        .call();
    };

    let messageEle = (<div className="text-danger">Do you want to delete these records?</div>);
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  render() {
    const { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className='flex-hbox'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
                options={[500, 1000, 2000, 5000]}
                optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
                onInputChange={this.onModify} />
            </div>

            <input.WStringInput className={'flex-hbox'} style={{ maxWidth: 300 }}
              name='search' value={this.filter.pattern}
              placeholder={('Enter Network or Event...')} onChange={this.onChangePattern} />

          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onAdd} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap} >
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>

            <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen() || !moderatorCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>

            {
              this.listRef.current ?
                <XLSXButton appContext={appContext} pageContext={pageContext} context={this.listRef.current.getVGridContext()} />
                : null
            }
          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UIAnnualConferenceList ref={this.listRef}
            appContext={appContext} pageContext={pageContext} plugin={this.plugin} onModifyBean={this.onModifyBean} />
        </div>
      </div>
    )
  }
}

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model["fileName"] = 'Annual Conference.xlsx'
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button laf={"info"} onClick={this.onExportCustomization} className="border-0 border-end rounded-0 p-1 mx-1"
        style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}
