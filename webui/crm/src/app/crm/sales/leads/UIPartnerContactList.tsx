import React from "react";
import * as FeatherIcon from 'react-feather';
import { grid, bs, entity, app } from '@datatp-ui/lib';
import { T } from "../backend";


const SESSION = app.host.DATATP_HOST.session;
export class UIPartnerContactListEditor extends entity.VGridEntityListEditor {

  createVGridConfig(): grid.VGridConfig {

    const CELL_HEIGHT: number = 40;
    let config: grid.VGridConfig = {

      record: {
        dataCellHeight: CELL_HEIGHT,
        editor: {
          enable: true,
          supportViewMode: ['table'],
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'personalContact', label: T(`Personal Contact`), width: 250, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true }
          },
          {
            name: 'position', label: T(`Position`), width: 180, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true }
          },
          {
            name: 'phone', label: T(`Phone`), width: 150, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true }
          },
          {
            name: 'email', label: T(`Email`), width: 200, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true }
          },
          {
            name: 'note', label: T(`Note`), width: 400, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true }
          }
        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  onDelete = () => {
    this.vgridContext.model.removeSelectedDisplayRecords();
    this.forceUpdate();
  }

  onAdd = () => {
    let newBean = {
      inputAccountId: SESSION.getAccountId(),
      inputAccountLabel: SESSION.getAccountAcl().getFullName(),
      partnerType: 'AGENTS_APPROACHED'
    }
    this.vgridContext.model.addRecord(newBean);
    this.vgridContext.getVGrid().forceUpdateView();
  }

  render(): React.JSX.Element {

    return (
      <div className='flex-vbox'>

        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>
          <div className="flex-hbox justify-content-start align-items-center flex-grow-1">
            <h5>Partner Contact</h5>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='info' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onAdd()}>
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>
            <bs.Button laf='warning' className="border-0 p-1 rounded-0" outline
              onClick={() => this.onDelete()}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>
        </div>

        <div className='flex-vbox flex-grow-1 rounded-bottom'>
          <grid.VGrid context={this.vgridContext} />
        </div>
      </div>
    )
  }

}