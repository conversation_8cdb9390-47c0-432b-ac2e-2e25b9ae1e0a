import React from 'react';
import * as FeatherIcon from "react-feather";
import { bs, input, util, grid, entity, app } from '@datatp-ui/lib';

import { T } from '../../backend';
import {
  GridConfigFactory,
  PriceListConfig,
  PricePlugin,
  SearchParams,
  onShowUIRequestPricing,
  TransportPurpose,
  UIMailRequestPricing,
  WRateFinder, XLSXPriceExportButton,
  WRateFinderGridFilter,
  buildTooltipValues,
  TooltipField,
  UITruckContainerTreePlugin,
  FieldControl,
  WRateFinderProps
} from '../../../price';

import { SQuotationCreation, UIQuotationUtils } from '../QuotationUtils';
import { ContainerTypeUnit } from '../../../common/ContainerTypeUtil';
import { TransportationMode, TransportationTool } from 'app/crm/common';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';

const SESSION = app.host.DATATP_HOST.session;

const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  const date = util.TimeUtil.parseCompactDateTimeFormat(dateStr);
  return util.TimeUtil.format(date, 'DD/MM/YYYY HH:mm');
};

interface UITransportPriceListProps extends entity.DbEntityListProps {
  onModifyParams?: (plugin: PricePlugin) => void;
}
class UITransportPriceList extends entity.DbEntityList<UITransportPriceListProps> {

  createVGridConfig(): grid.VGridConfig {
    let { pageContext, type, readOnly, plugin } = this.props;

    const writePermission = pageContext.hasUserWriteCapability() && !readOnly;
    const writeCap = (writePermission && type === 'page') || pageContext.hasUserModeratorCapability();

    let priceConfig: PriceListConfig = GridConfigFactory.createPriceConfig(this);

    let pluginImp = plugin as PricePlugin;
    let mode: TransportationMode = pluginImp.mode;
    let isHDScreen: boolean = window.screen.width < 1300;

    let config: grid.VGridConfig;

    if (TransportationTool.isTruck(mode)) {

      let tooltipFields: TooltipField[] = [
        { key: 'pickupAddress', label: 'Pickup Address' },
        { key: 'deliveryAddress', label: 'Delivery Address' },
        { key: 'carrierLabel', label: 'Subcontractor' },
        { key: 'note', label: 'Note' },
        { key: 'targetReferenceLabel', label: 'Priority Price For Customers' },
      ]

      let destinationField: grid.FieldConfig = {
        name: 'destination', label: T('Destination'), width: 280, filterable: true, container: 'fixed-left',
        fieldDataGetter(record) {
          return record['deliveryAddress'] || 'N/A';
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          let record: any = dRec.record;
          let label = record[field.name] || '';
          let type = record['groupType'];

          if (type === 'DeliveryAddress') {
            label = record['deliveryAddress'] || 'N/A';
          }

          const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
            dRecord.model['collapse'] = !dRecord.model['collapse'];
            let displayRecordList = _ctx.model.getDisplayRecordList();
            if (displayRecordList instanceof grid.TreeDisplayModel) {
              displayRecordList.updateDisplayRecords();
              _ctx.getVGrid().forceUpdateView();
            }
          }

          let childrenCount = dRec.model.children.length || '';

          const renderDeliveryProvince = () => (
            <div className="d-flex align-items-center" onClick={() => onCollapseRecord(dRec)}
              style={{ cursor: 'pointer', userSelect: 'text' }}>
              <span className={`d-flex align-items-center px-1 py-1 text-primary fw-semibold`}>
                <FeatherIcon.MapPin size={14} className="me-1" />
                <span style={{
                  width: '280px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden'
                }}>
                  {util.text.formater.uiTruncate(label, 280, true)}
                  {childrenCount > 0 && <span className="ms-1 text-secondary">({childrenCount})</span>}
                </span>
              </span>
            </div>
          );

          //TODO: For type === 'DeliveryAddress':
          // 1. Calculate the full text width of deliveryAddress
          // 2. If text width > 280px, wrap text to new lines
          // 3. Word wrapping rules:
          //    - Break only at whitespace
          //    - No word splitting/truncation
          //    - Maintain 280px width constraint
          // 4. Adjust row height automatically based on wrapped text

          const { htmlFormat } = buildTooltipValues(record, tooltipFields)
          const renderDeliveryAddress = () => (
            <bs.CssTooltip width={400} offset={{ x: 280, y: 0 }}>
              <bs.CssTooltipToggle>
                <div className="d-flex align-items-center px-1 py-1"
                  onClick={() => onCollapseRecord(dRec)}>
                  <span style={{
                    width: '280px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    overflow: 'hidden',
                  }}>
                    {label}
                  </span>
                </div>
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent className="d-flex flex-column rounded" >
                {htmlFormat}
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          );

          if (type === 'DeliveryProvince') {
            return renderDeliveryProvince();
          } else {
            return renderDeliveryAddress();
          }
        }
      }

      let fields = priceConfig.fields.filter(field =>
        field.name !== 'pickupLocationCode' &&
        field.name !== 'pickupAddress' &&
        field.name !== 'deliveryAddress'
      );

      // Replace deliveryLocationCode with destinationField
      const deliveryIndex = fields.findIndex(field => field.name === 'deliveryLocationCode');
      if (deliveryIndex !== -1) {
        fields[deliveryIndex] = destinationField;
      }

      const CELL_H = 40;
      const CELL_PARENT_H = 28

      config = {
        record: {
          computeDataRowHeight(ctx, dRecord) {
            const record = dRecord.record;
            const type = record['groupType'];
            if (type === 'DeliveryProvince') {
              return CELL_PARENT_H;
            } else {
              const val = record['deliveryAddressNormalized'] || 'N/A';
              const fontSize = 0.875 * 16; // Convert rem to pixels (assuming 1rem = 16px)
              const width = 270;
              const lineHeight = fontSize * 1.2; // Assuming line height is 1.2 times the font size
              const lines = Math.ceil(val.length * fontSize / width); // Estimate number of lines
              const height = lines * lineHeight + 5; // Add 5px as instructed
              return height > CELL_H ? height : CELL_H;
            }
          },
          editor: {
            supportViewMode: writeCap ? ['table'] : [],
          },
          fields: fields,
          fieldGroups: isHDScreen ? undefined : priceConfig.fieldGroups,
        },
        toolbar: { hide: true, },
        view: {
          currentViewName: 'tree',
          availables: {
            table: { viewMode: 'table' },
            tree: {
              viewMode: 'tree',
              label: 'Tree View',
              treeField: 'destination',
              plugin: new UITruckContainerTreePlugin()
            }
          }
        },
      }
    } else {


      if (TransportationTool.isSeaFCL(mode) || TransportationTool.isSeaLCL(mode)) {

        let feedbackCol: grid.FieldConfig = {
          name: 'feedbacks', label: 'Act.', width: 50, filterable: false, container: 'fixed-right',
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
            let record: any = dRec.record;
            let val = record[field.name] || '';
            const uiRoot = _ctx.uiRoot as UITransportPriceList;
            const { appContext, pageContext } = uiRoot.props;

            // like [{ loginId: dan, name: 'Dan Nguyen', feedbackDate: '2024-01-01', feedback: 'Good price'}]
            let feedbackObj: any[] = [];
            try {
              feedbackObj = typeof val === 'string' && val ? JSON.parse(val) : (val || []);
            } catch (error) {
              console.error('Invalid JSON in feedback:', val);
            }

            const currentAccountId = SESSION.getAccountId();
            let newFeedback: any = feedbackObj.find(item => item.salemanAccountId === currentAccountId);

            if (!newFeedback) {
              newFeedback = {
                salemanAccountId: currentAccountId,
                salemanLabel: SESSION.getAccountAcl().getFullName(),
                feedbackDate: util.TimeUtil.toCompactDateTimeFormat(new Date()),
                feedback: 'Good price'
              };
            }

            const onAddPopupFeedback = () => {

              const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

                const onAddFeedback = () => {
                  newFeedback.feedbackDate = util.TimeUtil.toCompactDateTimeFormat(new Date());
                  const existingIndex = feedbackObj.findIndex(item => item.salemanAccountId === newFeedback.salemanAccountId);
                  if (existingIndex >= 0) {
                    feedbackObj.splice(existingIndex, 1); // Xóa phản hồi cũ
                    feedbackObj.unshift(newFeedback); // Thêm vào đầu danh sách
                  } else {
                    feedbackObj.unshift(newFeedback); // Thêm vào đầu danh sách
                  }
                  // Update the record with new feedback array
                  record[field.name] = JSON.stringify(feedbackObj);

                  let saveModified = {
                    records: [record],
                    mode: pluginImp.mode
                  };

                  appContext.createHttpBackendCall('TransportPriceMiscService', 'saveModifiedPrice', { saveModified: saveModified })
                    .withSuccessData((_data: any) => {
                      appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
                      pageCtx.back();
                      uiRoot.getVGridContext().getVGrid().forceUpdateView();
                    })
                    .call();
                  _ctx.getVGrid().forceUpdateView();
                }

                return (
                  <div className='flex-vbox p-1'>
                    <div className='flex-vbox'>
                      <bs.Row>
                        <bs.Col span={6}>
                          <BBRefCrmUserRole style={{ minWidth: 250 }} minWidth={400} label='Saleman'
                            appContext={appCtx} pageContext={pageCtx} bean={newFeedback} disable
                            beanIdField='salemanAccountId' beanLabelField='salemanLabel'
                            placeholder='Saleman.' hideMoreInfo
                            onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                              bean['salemanAccountId'] = selectOpt['accountId'];
                              bean['salemanLabel'] = selectOpt['fullName'];
                            }} />
                        </bs.Col>
                        <bs.Col span={6}>
                          <input.BBDateTimeField
                            bean={newFeedback} field={'feedbackDate'} label={T('Feedback Date')}
                            dateFormat={"DD/MM/YYYY"} timeFormat={"HH:mm:ss"} disable />
                        </bs.Col>
                      </bs.Row>
                      <bs.Row>
                        <bs.Col span={12}>
                          <input.BBTextField label={T('Feedback')} style={{ height: '7em' }} bean={newFeedback} field={'feedback'} />
                        </bs.Col>
                      </bs.Row>
                    </div>

                    <div className='flex-hbox flex-grow-0 justify-content-end align-items-center mt-1'>
                      <bs.Button laf='info' outline className="px-2 py-1" style={{ width: 100 }} onClick={onAddFeedback}>
                        <FeatherIcon.Check size={12} className='me-1' /> OK
                      </bs.Button>
                    </div>
                  </div>
                )
              };
              pageContext.createPopupPage('price-feedback', T('Feedback'), createPageContent, { size: 'md', backdrop: 'static' });
            }
            return (
              <div>
                {!feedbackObj || Object.keys(feedbackObj).length === 0 ? (
                  <bs.Popover className='flex-grow-0' closeOnTrigger='button'>
                    <bs.PopoverToggle laf='info' outline className='border-0 py-2 px-1'>
                      <FeatherIcon.MessageSquare size={12} className="text-secondary me-1" />
                    </bs.PopoverToggle>
                    <bs.PopoverContent>
                      <div className='flex-vbox gap-1' style={{ width: '150px' }}>
                        <bs.Button laf='warning' outline className='border-0 py-2 px-2 w-100 text-start rounded-0'
                          onClick={() => this.onResendPricingRequest(dRec.record)}>
                          <FeatherIcon.Mail size={12} className="me-1" /> Request Pricing
                        </bs.Button>
                        <bs.Button laf='info' outline className='border-0 py-2 px-2 w-100 text-start rounded-0'
                          onClick={onAddPopupFeedback}>
                          <FeatherIcon.MessageSquare size={12} className="text-secondary me-1" /> Feedback
                        </bs.Button>
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                ) :
                  <bs.CssTooltip width={400} position='bottom-left' offset={{ x: -400, y: -100 }}>
                    <bs.CssTooltipToggle>
                      <bs.Popover className='flex-grow-0' closeOnTrigger='button'>
                        <bs.PopoverToggle laf='info' outline className='border-0 py-2 px-1'>
                          <FeatherIcon.MessageCircle size={14} className="text-primary me-1 fw-bold" />
                        </bs.PopoverToggle>
                        <bs.PopoverContent>
                          <div className='flex-vbox gap-1' style={{ width: '150px' }}>
                            <bs.Button laf='warning' outline className='border-0 py-2 px-1 w-100 text-start rounded-0'
                              onClick={() => this.onResendPricingRequest(dRec.record)}>
                              <FeatherIcon.Mail size={12} className="me-1" /> Request Pricing
                            </bs.Button>
                            <bs.Button laf='info' outline className='border-0 py-2 px-1 w-100 text-start rounded-0'
                              onClick={onAddPopupFeedback}>
                              <FeatherIcon.MessageSquare size={12} className="text-secondary me-1" /> Feedback
                            </bs.Button>
                          </div>
                        </bs.PopoverContent>
                      </bs.Popover>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent>
                      <div className='flex-vbox' style={{ minHeight: '200px', maxHeight: '400px' }}>

                        <bs.GreedyScrollable className="my-1">
                          {feedbackObj.map((item, index) => (
                            <div className="tooltip-header" key={index} style={{
                              fontSize: '0.95em',
                              color: '#198754',
                              borderBottom: (feedbackObj.length > 1 && index < feedbackObj.length - 1) ? '1px solid #dee2e6' : 'none'
                            }}>
                              <div className='flex-hbox justify-content-between align-items-center py-1 mt-1'>
                                <span className='fw-bold'>
                                  {item.salemanLabel}
                                </span>
                                <span>
                                  {formatDate(item.feedbackDate)}
                                </span>
                              </div>
                              <div className="mb-1 text-secondary">{item.feedback}</div>
                            </div>
                          ))}
                        </bs.GreedyScrollable>

                        <div className='flex-hbox justify-content-between align-items-center flex-grow-0 gap-2 border-top p-1'>
                          <bs.Button laf='warning' outline className='border-0 py-2 px-1 w-100'
                            onClick={() => this.onResendPricingRequest(dRec.record)}>
                            <FeatherIcon.Mail size={12} className="me-1" /> Request Pricing
                          </bs.Button>
                          <bs.Button laf='info' outline className='border-0 py-2 px-1 w-100'
                            onClick={onAddPopupFeedback}>
                            <FeatherIcon.MessageSquare size={12} className="text-secondary me-1" /> Feedback
                          </bs.Button>
                        </div>
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                }
              </div>
            )
          }
        }

        priceConfig.fields.push(feedbackCol);
      }

      config = {
        id: `wcrm-rate-finder:price-list-${mode}-${pluginImp.getPurpose()}-${pluginImp.getGroupType()}`,
        record: {
          dataCellHeight: isHDScreen ? 30 : 40,
          editor: {
            supportViewMode: writeCap ? ['table'] : [],
          },
          fields: priceConfig.fields,
          fieldGroups: isHDScreen ? undefined : priceConfig.fieldGroups,
        },
        toolbar: {
          hide: true,
          actions: [], filters: []
        },
        view: {
          currentViewName: 'table',
          availables: {
            table: { viewMode: 'table' },
          }
        },
      }
    }
    return config;
  }

  onResendPricingRequest = (price: any) => {
    let isMobile: boolean = bs.ScreenUtil.isMobileScreen();
    if (isMobile) return;
    const { plugin } = this.props;
    let pluginImp = plugin as PricePlugin;
    let requestModel: any = pluginImp.toSearchParams().mailRequestBuilder();

    requestModel = {
      ...requestModel,
      salemanBranchName: SESSION.getCurrentCompanyContext()?.companyCode,
      fromLocationCode: price.fromLocationCode,
      fromLocationLabel: price.fromLocationLabel,
      toLocationCode: price.toLocationCode,
      toLocationLabel: price.toLocationLabel,
      cargoReadyDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      shipmentDetail: {
        commodity: price.commodity
      }
    }

    return onShowUIRequestPricing(this, requestModel);
  }

  onPricingRequest = () => {
    let isMobile: boolean = bs.ScreenUtil.isMobileScreen();
    if (isMobile) return;
    const { plugin } = this.props;
    let pluginImp = plugin as PricePlugin;
    let requestModel: any = pluginImp.toSearchParams().mailRequestBuilder();
    requestModel['shipmentDetail'] = { commodity: 'GENERAL' }
    requestModel['salemanBranchName'] = SESSION.getCurrentCompanyContext()?.companyCode;
    return onShowUIRequestPricing(this, requestModel);
  }

  onRequestQuote = () => {
    const { plugin } = this.props;
    let pluginImp = plugin as PricePlugin;
    let mode: TransportationMode = pluginImp.mode;

    let records: any = this.vgridContext.model.getSelectedRecords();
    let paramSearch: SearchParams = pluginImp.toSearchParams();
    let inquiry: any = paramSearch.toInquiry();
    if (TransportationTool.isSeaFCL(mode)) {
      let volumeInfo = ContainerTypeUnit.toContainerString([ContainerTypeUnit._20DC]);
      inquiry['containerTypes'] = volumeInfo;
      inquiry['containers'] = ContainerTypeUnit.textToContainerList(volumeInfo);
    }

    let quotation: SQuotationCreation = {
      inquiry: inquiry,
      priceReferenceIds: [],
      isAutoSaved: true,
      inquiryRequestId: undefined
    }

    if (records.length === 0) {
      UIQuotationUtils.createNewSpecificQuotation(this, quotation);
    } else {

      const firstRecord = records[0];

      const matchedRoute = records.every((record: any) =>
        record.fromLocationCode === firstRecord.fromLocationCode &&
        (record.toLocationCode === firstRecord.toLocationCode ||
          (record.finalTerminalLocationLabel &&
            firstRecord.finalTerminalLocationLabel &&
            record.finalTerminalLocationLabel === firstRecord.finalTerminalLocationLabel))
      );

      if (!matchedRoute) {
        bs.dialogShow('Message',
          <div className="text-danger text-center p-2">
            Cannot request quote for records with different origin/destination locations
          </div>
        );
        return;
      }

      inquiry = {
        ...inquiry,
        fromLocationCode: firstRecord['fromLocationCode'],
        fromLocationLabel: firstRecord['fromLocationLabel'],
        toLocationCode: firstRecord['toLocationCode'],
        toLocationLabel: firstRecord['toLocationLabel'],
        finalDestination: firstRecord['finalTerminalLocationLabel']
      }
      quotation['inquiry'] = inquiry;

      let ids = this.vgridContext.model.getSelectedRecordIds();
      quotation['priceReferenceIds'] = ids;
      UIQuotationUtils.createNewSpecificQuotation(this, quotation);
    }
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin, onModifyParams } = this.props;
    let pluginImp = plugin as PricePlugin;
    let searchParam = pluginImp.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    pluginImp.searchParams = searchParam;
    if (onModifyParams) onModifyParams(pluginImp)
    else {
      this.reloadData();
      this.forceUpdate();
    }
  };

  renderPricingRequestButton() {
    let isMobile: boolean = bs.ScreenUtil.isMobileScreen();
    if (isMobile) {
      const { appContext, pageContext, plugin } = this.props;
      let pluginImp = plugin as PricePlugin;
      let requestModel = pluginImp.toSearchParams().mailRequestBuilder();

      return (
        <bs.OffCanvas offcanvasId={`offcanvas-demo-start`}>
          <bs.OffCanvasToggle className="border-0 p-1 btn-outline-warning" style={{ width: '150px' }} >
            <FeatherIcon.Mail size={12} /> Request Pricing
          </bs.OffCanvasToggle>
          <bs.OffCanvasContent placement={'start'} className=''>
            <div className="offcanvas-header">
              <h5 className="offcanvas-title">Request Pricing</h5>
              <button className="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"> </button>
            </div>
            <div className="offcanvas-body">
              <UIMailRequestPricing appContext={appContext} pageContext={pageContext}
                observer={new entity.ComplexBeanObserver(requestModel)} />
            </div>
          </bs.OffCanvasContent>
        </bs.OffCanvas>
      )
    } else {
      return (
        <bs.Button laf='warning' className="border-0 p-1" outline style={{ width: '150px' }}
          onClick={this.onPricingRequest}>
          <FeatherIcon.Mail size={12} /> Request Pricing
        </bs.Button>
      )
    }
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    const { plugin, appContext, pageContext } = this.props;
    let pluginImp = plugin as PricePlugin;
    let mode: TransportationMode = pluginImp.mode;
    let purpose: TransportPurpose = pluginImp.getPurpose();

    let searchParam = pluginImp.getSearchParams();
    let params: any = searchParam['params'] || {}

    return (
      <div className='flex-vbox'>

        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox'>

            <FieldControl context={this.vgridContext} />

            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>

            {TransportationTool.isSeaFCL(mode) && purpose === 'EXPORT' && !bs.ScreenUtil.isMobileScreen()
              ? <input.BBSelectField className="fw-bold text-warning border-bottom border-end text-center"
                style={{ width: 80 }} bean={params}
                field={"groupType"}
                options={['NONE_US', 'US_ROUTE', 'SPECIAL']} onInputChange={this.onModify} />
              : <></>
            }
            {TransportationTool.isTruck(mode) && !bs.ScreenUtil.isMobileScreen()
              ? <input.BBSelectField className="fw-bold text-warning border-bottom border-end text-center"
                style={{ width: 80 }} bean={params} field={"groupType"}
                options={['Logistics', 'Cross_Border']} optionLabels={['Logistics', 'Cross Border']}
                onInputChange={this.onModify} />
              : <></>
            }
            <WRateFinderGridFilter context={this.vgridContext} />

          </div>

          <div className="flex-hbox justify-content-end align-items-center">

            <XLSXPriceExportButton outline className='border-0 p-1'
              context={this.vgridContext} appContext={appContext} pageContext={pageContext} />

            {this.renderPricingRequestButton()}

            {!bs.ScreenUtil.isMobileScreen() &&
              <>

                {/* <bs.Button laf='info' className="border-0 p-1" outline style={{ width: '150px' }}
                  onClick={this.onExportQuote} hidden={bs.ScreenUtil.isMobileScreen()}>
                  <FeatherIcon.Download size={12} /> Export a Quote
                </bs.Button> */}

                <bs.Button laf='success' className="border-0 p-1" outline style={{ width: '150px' }}
                  onClick={this.onRequestQuote} hidden={bs.ScreenUtil.isMobileScreen()}>
                  <FeatherIcon.Plus size={12} /> Request a Quote
                </bs.Button>
              </>
            }

          </div>

        </div>
        {this.renderUIGrid()}
      </div>
    )
  }
}

export class WCRMRateFinder extends WRateFinder {


  constructor(props: WRateFinderProps) {
    super(props);

    // Get saved state from local storage
    let savedState;
    try {
      const storedState = localStorage.getItem('crm-price:rate-finder-state');
      savedState = storedState ? JSON.parse(storedState) : {
        activeTab: TransportationMode.SEA_FCL,
        type: 'IMPORT',
        enableEditor: this.enableEditor
      };
      // Cast activeTab to TransportationMode
      savedState.activeTab = savedState.activeTab as TransportationMode;
    } catch (e) {
      savedState = {
        activeTab: TransportationMode.SEA_FCL,
        type: 'IMPORT',
        enableEditor: this.enableEditor
      };
    }

    if (TransportationTool.isTruckContainer(savedState.activeTab)) {
      savedState.type = 'Container'
    } else if (TransportationTool.isTruckRegular(savedState.activeTab)) {
      savedState.type = 'Truck';
    }

    this.state = savedState;

    const { initSearchParam } = this.props;
    this.searchParam = initSearchParam ? initSearchParam : new SearchParams(savedState.type, savedState.activeTab);
    this.searchParam.effectiveDate = util.TimeUtil.javaCompactDateTimeFormat(new Date());
  }

  override onModify = (_bean: any, _field: string, _oldVal: any, newVal: any): void => {
    this.mainViewId = util.IDTracker.next();
    const { onModify } = this.props;
    this.enableEditor = false
    const newState = { activeTab: this.state.activeTab, type: this.state.type, enableEditor: this.enableEditor };
    this.saveStorageState(newState);

    if (onModify) onModify(this.searchParam)
    else this.forceUpdate();
  };

  renderTransportPriceList = () => {
    const { appContext, pageContext } = this.props;
    return (
      <UITransportPriceList
        plugin={this.searchParam.pluginBuilder()} appContext={appContext} pageContext={pageContext}
        onModifyParams={(_plugin: PricePlugin) => {
          this.searchParam.groupType = _plugin.getGroupType();
          this.searchParam.maxReturn = _plugin.getMaxReturn();
          this.onModify(this.searchParam, 'groupType', null, _plugin.getGroupType())
        }} />
    )
  }

}

