import {
  CustomClearanceType,
  ImportExportPurpose,
  SeaType,
  TransportationMode,
  TruckType
} from "./model";

export const TransportationTool = {

  isUnknown(mode: TransportationMode) {
    return mode === TransportationMode.UNKNOWN;
  },

  isAir(mode: TransportationMode) {
    return mode === TransportationMode.AIR;
  },

  isRail(mode: TransportationMode) {
    return mode === TransportationMode.RAIL;
  },

  isSea(mode: TransportationMode) {
    return mode === TransportationMode.SEA_FCL || mode === TransportationMode.SEA_LCL;
  },

  isTruck(mode: TransportationMode) {
    return this.isTruckRegular(mode) || this.isTruckContainer(mode);
  },

  isSeaLCL(mode: SeaType | TransportationMode) {
    return mode === SeaType.LCL || mode === TransportationMode.SEA_LCL;
  },

  isSeaFCL(mode: SeaType | TransportationMode) {
    return mode === SeaType.FCL || mode === TransportationMode.SEA_FCL;
  },

  isTruckRegular(mode: TruckType | TransportationMode) {
    return mode === TruckType.REGULAR || mode === TransportationMode.TRUCK_REGULAR;
  },

  isTruckContainer(mode: TruckType | TransportationMode) {
    return mode === TruckType.CONTAINER || mode === TransportationMode.TRUCK_CONTAINER;
  },

  mapDisplayValue(mode: TransportationMode): string {
    let displayVal = '';
    if (TransportationTool.isAir(mode)) {
      displayVal = "Air";
    } else if (TransportationTool.isRail(mode)) {
      displayVal = "Rail Way";
    } else if (TransportationTool.isSeaFCL(mode)) {
      displayVal = "FCL";
    } else if (TransportationTool.isSeaLCL(mode)) {
      displayVal = "LCL";
    } else if (TransportationTool.isTruckContainer(mode)) {
      displayVal = "Container";
    } else if (TransportationTool.isTruckRegular(mode)) {
      displayVal = "Truck";
    } else {
      displayVal = mode;
    }
    return displayVal;
  },

  hasCargo(mode: TransportationMode) {
    return (mode === TransportationMode.SEA_LCL
      || mode === TransportationMode.AIR
      || mode === TransportationMode.TRUCK_REGULAR);
  },

  hasContainer(mode: TransportationMode) {
    let hasContainer = mode === TransportationMode.SEA_FCL || mode === TransportationMode.TRUCK_CONTAINER ||
      mode === TransportationMode.SEA_LCL || mode === TransportationMode.RAIL;
    return hasContainer;
  },
}

export const CustomClearanceTool = {
  isCustomClearanceSeaLCL(type: CustomClearanceType) {
    return type === CustomClearanceType.SEA_LCL;
  },

  isCustomClearanceSeaFCL(type: CustomClearanceType) {
    return type === CustomClearanceType.SEA_FCL;
  },

  isCustomClearanceSea(type: CustomClearanceType) {
    return type === CustomClearanceType.SEA_FCL || type === CustomClearanceType.SEA_LCL;
  },

  isCustomClearanceAir(type: CustomClearanceType) {
    return type === CustomClearanceType.AIR;
  },

  isCustomClearanceRail(type: CustomClearanceType) {
    return type === CustomClearanceType.RAIL;
  },

  isCustomClearanceTruck(type: CustomClearanceType) {
    return type === CustomClearanceType.TRUCK_CONTAINER || type === CustomClearanceType.TRUCK_REGULAR;
  },

  isCustomClearanceTruckContainer(type: CustomClearanceType) {
    return type === CustomClearanceType.TRUCK_CONTAINER;
  },

  isCustomClearanceTruckRegular(type: CustomClearanceType) {
    return type === CustomClearanceType.TRUCK_REGULAR;
  },

  mapDisplayValue(type: CustomClearanceType) {
    if (this.isCustomClearanceAir(type)) return 'Custom Air';
    else if (this.isCustomClearanceSeaFCL(type)) return 'Custom Sea FCL';
    else if (this.isCustomClearanceSeaLCL(type)) return 'Custom Sea LCL';
    else if (this.isCustomClearanceRail(type)) return 'Custom Rail';
    else if (this.isCustomClearanceTruckContainer(type)) return 'Custom Truck Container';
    else if (this.isCustomClearanceTruckRegular(type)) return 'Custom Truck Regular';

    else return 'Unknown';
  }
}

export const PurposeTool = {

  isExport(purpose: ImportExportPurpose) {
    return purpose === ImportExportPurpose.EXPORT;
  },

  isImport(purpose: ImportExportPurpose) {
    return purpose === ImportExportPurpose.IMPORT;
  },

  isDomestic(purpose: ImportExportPurpose) {
    return purpose === ImportExportPurpose.DOMESTIC;
  },

  mapDisplayValue(purpose: ImportExportPurpose) {
    if (this.isExport(purpose)) return 'Exp';
    else if (this.isImport(purpose)) return 'Imp';
    else if (this.isDomestic(purpose)) return 'Log';
    else return purpose ? purpose : 'Unknown';
  }

}

export function mapToTypeOfShipment(purpose: ImportExportPurpose, mode: TransportationMode) {
  let purposeVal = PurposeTool.mapDisplayValue(purpose) || '';
  let modeVal = TransportationTool.mapDisplayValue(mode) || '';
  return modeVal + ' ' + purposeVal
}