import React from "react";
import * as FeatherIcon from 'react-feather'

import { bs, input, entity, util, app } from "@datatp-ui/lib";
import { T } from "../Dependency";
import { BBRefCountry, BBRefLocation, BBRefState } from "module/settings";

const SESSION = app.host.DATATP_HOST.session;

export class UIPartnerCompanyEditor extends entity.AppDbEntityEditor {

  state = {
    isSending: false
  };

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let partner = observer.getMutableBean();

    partner['dateModified'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    if (partner['accountCreatorId']) {
      partner['accountCreatorId'] = SESSION.getAccountId();
      partner['accountCreatorLabel'] = SESSION.getAccountAcl().getFullName();
    }

    let missingFields: string[] = [];
    if (!partner['email'] || !partner['phone']) missingFields.push('Email, Phone');
    if (!partner['address']) missingFields.push('Address');
    if (!partner['countryId']) missingFields.push('Country');
    if (!partner['name']) missingFields.push('Partner Name (Abb)');
    if (!partner['fullName']) missingFields.push('Partner FullName');

    if (missingFields.length > 0) {
      bs.dialogShow('Missing Information',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Please provide: ${missingFields.join(', ')}.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error(`Please provide: ${missingFields.join(', ')}.`);
    }

    this.setState({ isSending: true });
  }

  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('PartnerService', 'getByTaxCode', { taxCode: newVal })
      .withSuccessData((partner: any) => {
        if (!partner || Object.keys(partner).length === 0) return;
        let message = (
          <div className="ms-1 text-warning text-center py-3 border-bottom">
            A partner with the tax code "{newVal}" already exists in the system.
          </div>
        );
        bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'md' });
        return false;
      })
      .call();
  }

  render() {
    let { observer, appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let partner = observer.getMutableBean();
    partner['accountCreatorId'] = SESSION.getAccountId();
    partner['accountCreatorLabel'] = SESSION.getAccountAcl().getFullName();
    const isNew = observer.isNewBean();

    return (
      <div className="flex-vbox">

        <div className='p-1'>
          {
            !isNew &&
            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField bean={partner} field='code' label={T("Partner Code")} disable />
              </bs.Col>

              <bs.Col span={6}>
                <input.BBDateTimeField
                  className='rdt-bottom' bean={partner} label={T('Date Created')}
                  field={'dateCreated'} dateFormat={"DD/MM/YYYY"} timeFormat={false} disable />
              </bs.Col>
            </bs.Row>
          }

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField required
                bean={partner} field='name' label={T("Name")} disable={!writeCap}
                placeholder='Title.....'
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  if (!bean['label']) bean['label'] = newVal;
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField
                placeholder='Company.....'
                bean={partner} field='fullName' label={T("Company (Fullname)")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='taxCode' label={T("Tax Code")} disable={!writeCap} required onBgInputChange={this.onCheckTaxCode} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='representative' label={T("Representatiive")} disable={!writeCap} />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='phone' label={T("Phone")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='email' label={T("Email.")} disable={!writeCap} required validators={[util.validator.EMAIL_VALIDATOR]} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <BBRefCountry key={util.IDTracker.next()}
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350} required
                disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                bean={partner} beanIdField={'countryId'} hideMoreInfo
                beanLabelField={'countryLabel'} refCountryBy='id'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['countryId'] = selectOpt['id'];
                  bean['countryLabel'] = selectOpt['label'];
                  this.forceUpdate();
                }}
              />
            </bs.Col>
            <bs.Col span={3}>
              <BBRefState key={util.IDTracker.next()}
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Province')} placeholder="Enter Province"
                bean={partner} beanIdField={'provinceId'} hideMoreInfo required
                beanLabelField={'provinceLabel'} countryId={partner.countryId} />
            </bs.Col>

            <bs.Col span={6}>
              <BBRefLocation label='KCN' minWidth={300} style={{ maxWidth: 800 }} placement='auto-start'
                appContext={appContext} pageContext={pageContext} bean={partner}
                beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
                disable={!writeCap} inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
                beanRefLabelField='label'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  if (selectOpt && selectOpt['id']) {
                    bean['countryId'] = selectOpt['countryId'];
                    bean['countryLabel'] = selectOpt['countryLabel'];
                    bean['provinceId'] = selectOpt['stateId'];
                    bean['provinceLabel'] = selectOpt['stateLabel'];
                    bean['kcnLabel'] = selectOpt['label'];
                    bean['address'] = selectOpt['address'];
                    this.forceUpdate();
                  }
                }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField
                bean={partner} label={T('Address')} field="address" disable={!writeCap}
                style={{ height: '4em' }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField bean={partner} label={T('Note')} field="note" disable={!writeCap} style={{ height: '8em' }} />
            </bs.Col>
          </bs.Row>

        </div>

        <bs.Toolbar className='border' hide={!writeCap}>

          <entity.ButtonEntityCommit className='py-1 px-3 mx-2' style={{ width: 120 }} btnLabel={`${this.state.isSending ? 'Saving...' : 'Save'}`}
            appContext={appContext} pageContext={pageContext}
            disable={this.state.isSending}
            observer={observer} hide={!writeCap}
            commit={{
              entityLabel: T(partner.code),
              context: 'company',
              service: 'PartnerService', commitMethod: 'savePartnerCompany'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />

        </bs.Toolbar>
      </div>
    );
  }
}

