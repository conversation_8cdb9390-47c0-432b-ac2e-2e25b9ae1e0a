import React from 'react';
import { grid, sql, entity, util, bs, app } from '@datatp-ui/lib'
import { T } from '../Dependency';
import { UIPartnerCompanyEditor } from './UIPartnerCompany';

export class PartnerCompanyListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'PartnerService',
      searchMethod: 'searchPartnerCompany',
      changeStorageStateMethod: 'changePartnerCompanyStorageState'
    }

    this.searchParams = {
      "filters": [...sql.createSearchFilter()],
      "optionFilters": [
        sql.createStorageStateFilter(
          [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "maxReturn": 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export class UIPartnerCompanyList extends entity.DbEntityList {

  createVGridConfig() {
    let { pageContext, type } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();

    let config: grid.VGridConfig = {
      title: T('Partner Company'),
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(true),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('name', T('Partner Name'), 300),
          // entity.DbEntityListConfigTool.FIELD_ON_SELECT('loginId', T('Login ID'), 100),
          // {
          //   name: 'name', label: T('Partner Name'), width: 300, filterable: true,
          //   format(val) {
          //     return util.text.formater.uiTruncate(val, 300, true);
          //   },
          // },
          {
            name: 'dateCreated', label: T(`Date Created`), width: 120, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record) {
              return util.text.formater.compactDate(record['dateCreated']);
            },
          },
          {
            name: 'fullName', label: T('Partner Fullname'), width: 300,
            format(val) {
              return util.text.formater.uiTruncate(val, 300, true);
            },
          },
          { name: 'taxCode', label: T('Tax Code'), width: 150 },
          { name: 'email', label: T('Email'), width: 150 },
          { name: 'representative', label: T('Representative'), width: 150 },
          { name: 'countryLabel', label: T('Country'), width: 150 },
          { name: 'accountCreatorLabel', label: T(`Creator`), width: 180 },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 120,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDate,
            fieldDataGetter: (record: any) => {
              if (record['modifiedTime'] === '-') return record['modifiedTime'];
              return util.text.formater.compactDate(record['modifiedTime']);
            },
          },
        ],
        fieldGroups: {},
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
            [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], type === 'selector' || !pageContext.hasUserWriteCapability()
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, "Del"),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
      },

      footer: {
        default: {
          hide: type === 'selector',
          render: (ctx: grid.VGridContext) => {
            let uiList = ctx.uiRoot as UIPartnerCompanyList;
            let { appContext, pageContext } = uiList.props;
            let writeCap = pageContext.hasUserWriteCapability();

            return (
              <bs.Toolbar className='border'>
                <entity.WButtonEntityNew hide={!writeCap}
                  appContext={appContext} pageContext={pageContext}
                  label={'Partner Company'} onClick={this.onNewPartnerCompany} />
              </bs.Toolbar>
            );
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select Partner"), type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        },
      }
    }
    return config;
  }

  onNewPartnerCompany = () => {
    let { pageContext } = this.props;
    let observer = new entity.BeanObserver({});
    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' >
          <UIPartnerCompanyEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
            onPostCommit={(_bean) => {
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
              pageCtx.back();
            }} />
        </div>
      )
    }
    pageContext.createPopupPage(`new-partner-company-${util.IDTracker.next()}`, `New Partner Company`, pageContent, { size: 'flex-lg', backdrop: 'static' });
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.getVGridContext().model.getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.dialogShow('Message',
        <div className="text-warning text-center p-2">
          No Partner Company Selected!
        </div>
      );
      return;
    }

    appContext.createHttpBackendCall('PartnerService', 'deletePartnerCompanies', { ids: selectedIds })
      .withSuccessData((_data: any) => {
        this.reloadData();
        this.getVGridContext().model.removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .withEntityOpNotification('delete', 'partner')
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let partner = dRecord.record;
    let { appContext, pageContext } = this.props;
    let param: any = { partnerId: partner.id };
    appContext.createHttpBackendCall('PartnerService', 'getPartnerCompany', param)
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIPartnerCompanyEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
              onPostCommit={(_bean) => {
                this.reloadData();
                pageCtx.back();
              }} />
          );
        }
        let pupupLabel: string = `Partner Company : ${partner.name}`;
        pageContext.createPopupPage(`partner-${util.IDTracker.next()}`, pupupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }

}
