---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]

### [R20250902]

I. CRM Maintenance:

1. [Nhat]
  - Cập nhật Agent Transaction List:
    + Search Filter: chỉnh lại form Search Filter, bổ sung search theo Route
    + Hiển thị dữ liệu Continent lên bảng
    + Dùng Tree List Group theo Country, Continent, Network. Hiển thị total ở từng Tree Root.
      * Continent: Nhóm theo thứ tự Continent > Country > Agent (Popup show Agent Transactions)
      * Country: Nhóm theo thứ tự Country > Agent (Popup show Agent Transactions)
      * Network: Nhóm theo thứ tự Network > Agent (Popup show Agent Transactions)
    + Footer: Hiển thị total volume theo Chi nhánh: HPH, HCM, ...

2. [An] + [Dan]
- Cập nhật chức năng export quotation
- Cập nhật CronJob CrmUserRole
- Cập nhật chức năng Apply Margin Quotation
- Tạo UI Partner Contact
- Cập nhật UI Request Pricing Trucking
- Cập nhật Customer Lead Sql
- <PERSON><PERSON><PERSON> năng lọc, xuất excel (ở User/ Company/ Admin)
- Req by Ms. Tuyen (BD) - Cập nhật thêm form bảng giá cho BD.
  - Rút gọn thông tin Local Charge.
  - Tách các thông tin cước.
  - Có tuỳ chọn để included thông tin KB, Costing vào note ở selling lúc gửi IB.

- Rename BFSOne Partner ==> CRM Partner.
- Vincent - Agent Potential InputTrường Contact/Phone/Email nên bổ sung chức năng nhập thêm contact vì 1 công ty có thể có nhiều PIC
- Vincent - Agent Potential InputThêm trường "Position" (optional)
- Vincent - Agent Potential InputNếu đã convert successfully từ LEAD sang Agent/Customer thì LEAD còn tồn tại ko?
    Tess: Agent đã win -> có code nhưng LEAD cũ không mất đi, nếu muốn xóa phải nhờ IT xóa case by case -> có chuyển hẳn code Lead sang code agent đã win luôn được ko (bỏ lead luôn) tránh sự trùng lặp
-  Tess - "Em nghĩ nên thay tên cột 20'DC và BL thành Unit price và Unit. Ở cột Unit có thể chọn được đơn vị 20'DC, 40'HC, set, shipment... như lúc nhập giá trên OF1
Để 20'DC và BL như hiện tại em thấy hơi khó hiểu và dễ nhầm lẫn cho khách"
- BD support - Thiếu VAT

II. Feature:

  - [Dan] - Implement Partner Account (Hộ trợ login bằng partner)
    => Xong Backend, UI bàn giao lại cho Quan.

  - [Dan] - Implement Partner Company (Chức năng quản lý partner cho acc login bằng Partner).
    => Xong curl Backend, UI. Đang review với a Hiếu.

  - [Dan] - Implement Partner Request - quy trình Partner (Quản lý yêu cầu tạo partner trên CRM)

  - [Dan] - Implement Partner Request - quy trình Partner (Quản lý yêu cầu tạo partner trên CRM)

  - [Nhat] + [Dan] - Implement công việc hàng ngày cho team BD Support.
    + Network Membership Fee: Quản lý chi phí tham gia các hiệp hội.
    + Annual Conference: Chi tiết lịch, event, người tham gia, note, ... tham gia hiệp hội.
    + Agency Agreement Follow Up: Quy trình công việc theo từng hợp đồng agent.
    + Tasks:
      + [Nhat] - Thêm màn hình List Network Membership Fee, Annual Conference, Agency Agreement Follow Up vào menu Agent.
        - Bổ sung các tính năng Edit/Filter/Group cho các màn hình trên.
        - Script: migrate:run --script crm/DropCRMTables.groovy.


### [R20250825]

1. [Nhat] - Cập nhật code bổ sung thêm field 'refund' cho request tạo BFSOnePartner.
  - Test/fix bug chức năng tạo Customer + request tạo BFSOne Customer; tạo Agent + request tạo BFSOne Agent.
  - Thêm màn hình list Co-Loader, bổ sung các chức năng tương tự Customer/Agent.
  - Migrate dữ liệu Coloader từ BFSOne vào DataTP.
  - Cập nhật document cho phần Coloader.
   [An]
  - Cập nhật hàm searchCustomerLeads
  - Clean code, chuyển Account, Employee -> CrmUserRole module crm
  - Tạo entity Subcontractor viết các chức năng
  - Cập nhật container, trucking theo subcontractor mới
  - Fixbug màn booking và quotation không hiển thị receiver
2. Script:
  - [An]
  - server:migrate:run --script crm/UpdateSubcontractor.groovy

### [R20250820]

1. [Nhat] - Cập nhật giao diện BD Reports - Volume Performance, cập nhật query, bổ sung các cột Profit, Revenue theo mệnh giá.
          - Bổ sung màn hình list HouseBill khi click vào saleman.
          - Script: server:migrate:run --script jobtracking/ImportData.groovy --company beehph
1. [An]
  - Cập nhật UI theo Feedback Inquiry hàng rời.
  - Cập nhật cron job CrmUserRole
  - Chức năng tạo quotation task ở màn hình new quotation
  - Chuyển Account -> CrmUserRole của InquiryRequest

1. [Dan] - Enhance, fix bugs Quotation, IB, BFSOne Partner.
- Thêm trường note cho Booking.
- Export data báo giá.
- Fix UI báo giá, tính toán margin.

### [R20250819]
4. [Nhat] - Cập nhật lại các logic sync, api, viết script để cập nhật lại trường CustomertypeID từ Partners (BFSOne) vào trường groupName của BFSOnePartner (Cloud).
  - Cập nhật UI hiển thị trường groupName cho Customer, ẩn đối với Agent, Coloader.
  - Script: migrate:run --script crm/AlterTables.groovy
            server:migrate:run --script crm/UpdateDataBFSOnePartner.groovy --company beehph

### [R20250818]

1. [Nhat] - Bỏ các thông tin nhạy cảm trên docs liên quan đến Lead/ Customer.
          - Tách thông tin Booking Entity, bỏ các relation One to One, One to Many, Migrate dữ liệu.
          - Tách thông tin các Booking Charge Entity (Sea, Air, Rail): bỏ các relation One to One, One to Many, Migrate dữ liệu.
          - Script: migrate:run --script crm/AlterTables.groovy
2. [An]
- Chức năng transfer Lead ở màn hình Company (CustomerLeadTree)
- Cập nhật theo Feedback Inquiry hàng rời.
- Cập nhật form Request Pricing của container và truck
- Viết groovy cập nhật lại hàm searchCommodityType
### [R20250813]

1. [Dan] - Req by Mr. Minh Sale - Chỉnh sửa lại UI Task Calendar, Task Customer.
  ```
    Tasks Customer:
      Để cái mới nhất, xong a xem cái cũ thì click lên.
      Xem lịch sử khách hàng show thêm thông tin địa chỉ.
  ```

2. [Nhat]
  - Remove hệ thống partner cũ, drop dữ liệu account, profile link với partner cũ.
  - Thay thế các field sử dụng loginId bằng accountId trong các entity module Account, Company, Security.
  - Cập nhật lại code back-end, front-end liên quan.
  - Migrate dữ liệu từ column sử dụng loginId sang accountId.
  - Script: migrate:run --script common/AlterTables.groovy

### [R20250811]

1.
- [An]
  - Chỉnh Key Account theo yêu cầu.
  - Viết BBRefCrmUserRole, thay BBRefEmployee -> BBRefCrmUserRole màn Customer Lead.
  - Viết script cập nhật dữ liệu CrmUserRole.
2. Script:
- [An] server:migrate:run --script crm/UpdateDataEmployee.groovy

3. [Nhat]
  - Thay thế các field sử dụng loginId bằng accountId trong các entity của module Communication, Company, DTable, Project. Các entity extends CompanyLoginPermission.
  - Cập nhật lại code back-end, front-end liên quan.
  - Migrate dữ liệu từ column sử dụng loginId sang accountId.
  - MigrateScript: migrate:run --script common/AlterTables.groovy

4. [Dan] - Them trường partner type cho partner (customer).
    - Chức năng xoá báo cáo Sale Performance.
    - Chức năng xuất excel Task Calendar.

5. [Dan] - Review code MSA, sync House bill.

### [R20250804]

- [Dan] - Fix UI Lead/ Agent Potential, Review code CRM.
  - Fix bugs Tạo, Transfer Lead, Agent Potential.
  - Cập nhật chức năng Sync Partner từ hệ thống BFSOne theo tax code, mã cho salesman.
  (Áp dụng với partner cũ được approve/ xin cấp thêm quyền trên BFSOne)

- [Dan] - Cập nhật code bee legacy, sync data house bill (2022 -> 2024)

- [An] - check script để work với database mới.
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan

### [R20250801]

- [Dan] - Agent Transactions: (Báo cáo giao dịch theo agent)
  - Fix query, enhance search, view raw data.

- [Dan] - Cập nhật input nhập thông tin Container.
- [Dan] - Bỏ chức năng xuất báo giá nhanh trên màn hình search giá.

- [Nhat] - Enhance màn hình Leads: Khi convert Lead => chuyển trạng thái Lead thành Converted => Popup giao diện Request to BFSOne Agent/Customer
         Script:
          migrate:run --script hr/UpdateData.groovy

### [R20250731]

- [Nhat] - Enhance màn hình lịch xe: Bổ sung thông `Start place`, `End place` lên màn hình calendar
          + Script: server:migrate:run --script crm/UpdateDataAsset.groovy --company beehph
                    migrate:run --script crm/AlterTables.groovy

- [Dan] - Enhance màn hình báo giá cho team BD.

- [An] - review, enhance code.
    - Hiển thị detail, phân quyền theo bộ phận task calendar cho màn hình company task (Quản lý).
    - Enhance query Agent Transaction gửi raw data qua client, thay vì gửi dữ liệu đã tổng hợp.

### [R20250730]
1. [Nhat]
  - Thêm tính năng Xuất Excel Raw Data cho KPI: xuất toàn bộ mục tiêu, kết quả + thông tin cơ bản của KPI.
  - Book Lịch Xe: Bổ sung tính năng thông báo qua Zalo, nội dung thông báo:
  `Bạn có lịch đặt xe:`
    `+ Ngày đi 01/01/2025`
    `+ Thời gian: 08:00 - 17:00`
    `+ Nội dung: Đi công tác`
    `+ Nơi đi: Hải Phòng`
    `+ Nơi đến: Hà Nội`
    `+ Người yêu cầu: Tiến HR`
  - Thêm bảng Job Tracking, phân quyền cho Team BEEHCM Sea Xuất (US + Non US)
    + Script: server:migrate:run --script jobtracking/InitJobTrackingData.groovy --company beehcm

- [An]
  - Tạo company Bee Taiwan chuyển employee của department Bee Taiwan từ Bee Corp qua company mới
  - Rename column bảng daily task, cập nhật dữ liệu vào field

2. Script:
- [An] server:migrate:run --script crm/CreateCompany.groovy --company bee
- [An] migrate:run --script crm/RenameCRMColumn.groovy

### [R20250729]

- [Nhat] - Clean Code, kiểm tra + cập nhật các query sau khi tách database
        + Script:
            migrate:run --script crm/AlterTables.groovy
            server:migrate:run --script crm/UpdateIntegratedPartner.groovy --company bee

- [An] - Check script sync customer leads để work với database mới.
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan

- [Dan] - Tổ chức lại code Backend, Frontend CRM, bỏ phụ thuộc dự án datatp-logistics.

### [R20250727]

1. Tasks:

- [Dan] - Inquiry Request:
    - Enhance lại form để cho phép gửi request theo nhiều tuyến. (MultiEntityRef).
    - Cập nhật lại docs hướng dẫn requests.

- [Dan] - Enhance check Lead/ check partner tax code.
  - Bổ sung thêm check theo tên, check thêm dữ liệu agent potential, highlight keywords.

- [Dan] - Tách code, dependency, review query, tách database CRM app.
    - Review, tổ chức lại code UI CRM.

- [An] -Request Mr. Duy (HPH) - Cập nhật bảng giá trucking container.
    - Thay đổi mức giá cho các loại cont 20', 40'.
    - Cập nhật UI/ logic, template excel để input dữ liệu.

2. Script:

### [R20250725]

- [An]  Task Calendar:
  - Cron Job: Tự động tích hoàn thành công việc (sale task calendar) vào cuối mỗi tuần.

- [An] - Phân quyền theo app space, cập nhật thêm trường note.

- [Nhat] - Cập nhật query Customer Map, thay vì merger dữ từ 2 dataSource(db_bee_legacy.integrated_house_bill + datatp.bfsone_partner), dùng dataSource db_bee_legacy(integrated_house_bill + integrated_partner)

- [NHAT] [An] - Clean code, viết script DROP Quotation Air/Sea/Truck/CustomClearance Charge + DELETE data trong các entity liên quan:
        TransportFrequency, CustomerAirTransportAdditionalCharge, CustomerAirTransportCommissionDistribution
        CustomerSeaTransportCommissionDistribution, CustomerSeaTransportAdditionalCharge,
        CustomerTruckTransportAdditionalCharge, CustomerTruckTransportCommissionDistribution
        + Script: migrate:run --script crm/AlterTables.groovy

- [Nhat] - Cập nhật giao diện tạo Lịch xe/Phòng họp + giao diện Approval Lịch xe

- [Dan] - Inquiry Request.
  - Thêm cột clientPartnerType: Sale khi báo giá có thể báo cho partner với các loại khác nhau như Agent, Customer Lead, Customer.
  - Cần thêm flag (clientPartnerType) để phân biệt sales đang báo giá cho loại partner nào, với từng loại partner,
    thông tin lúc gửi request (chung 1 partner) -> Quotation/ Booking yêu cầu thông tin riêng biệt (Agent, Customer, ... ) bóc tách rõ ràng ra.
  - Cập nhật lại thông tin IB gửi qua BFSOne nếu đối tượng sales báo giá là Agent.
      - Sellling Payer => Agent.
      - Client (để sales tự nhập)
      - Agent (Lấy thông tin từ request / báo giá).

### [R20250724]

1. [Nhat] High - Xử lý dữ liệu partners lost cho HCM
          Dữ liệu Tâm gửi dạng excel, gồm các khách hàng đã lâu ko phát sinh booking.
          Y/c dev kiểm tra/ check lại với hệ thống, nếu không phát sinh booking trong 1 năm gần đây => Import vào CRM.
  - Migrate script: server:migrate:run --script crm/UpdateDataBFSOnePartner.groovy --company beehcm

### [R20250723]

1. [Dan] - Partner : Review code, enhance UI, update api integrate with BFSOne.

2. [Nhat] Làm việc, đấu nối với API Customer Lead a Quý gửi.
  - Tạo, cập nhật Lead ở DataTP -> tạo, cập nhật Lead ở BFSOne. => `DONE`
  - Kiểm tra synced Customer Leads (BFSOne) to DataTP. => `DONE`
  - Xoá Lead ở DataTP -> Xoá Lead ở BFSOne.
  - Transfer Lead ở DataTP -> Update Username Lead ở BFSOne.
  - [Nhat] + [An] - Script Migrate: Get data theo lead code ở bfsone -> cập nhật thông tin (tax code, ...) Lead ở DataTP. `DONE`
          + Script: migrate:run --script crm/AlterTables.groovy
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad

### [R20250721]

1. Tasks:
- [Dan] - fix bugs, review code.
  - Fix bugs api MSAIntegrationService -> msaApiUrl
  - Fix bugs, review code Update. Resend Internal Booking.
  - Fix bugs BBRefMultiEmail.

1. [Nhat]  - Salesman Activity Tracker, Task Calendar (Company).
  + Check lại phân quyền, filter theo data scope là group.
  + HCM: Issacc, a Thanh ở HCM chỉ nhìn thấy dữ liệu của team Sale 1.
  + HCM: Abby.vnsgn, a Phương nhìn sales toàn Team sales còn lại (trừ Team 1).
  + HPH: Chị Oanh: tasks của VP Nam Định.
  + HPH: Anh Tản, chị Hạnh: VP Hà Nam
  + HPH: Chị Thuý: VP Thanh Hoá.
  + HPH: THAI THI BINH DUONG: VP Nghệ An.

2. Script:


### [R20250719]

1. Quản lý Quy trình (Workflow Management) - theo dõi các requests phát sinh khi làm hàng...
  - Xem chi tiết ở Features.

  Tasks:
    1. [Dan] - Design code, chuẩn bị code, QA.
    2. [Nhat] - Function gửi mail:
        Khi tạo request -> Gửi mail cho người liên quan -> Approved/ Rejected qua mail ->
        hiển thị màn hình thông báo cho người dùng. -> Gửi kết quả Approved/Rejected qua mail cho người liên quan.
    3. [An] - Implement code, Backend, Frontend.
    4. [An] - Dashboard => Pending.

[Nhat] - Viết script migrate/ sync data bfsone_partner (datatp) -> integrated_partner (bee legacy)
            Continent tính toán từ hệ thống của mình, join theo data country_label của bfsone_partner, settings_country_group lấy đúng continent cấp đầu tiên (không có partner_id)
            + script: server:migrate:run --script crm/UpdateIntegratedPartner.groovy
        - Viết Cron Job cập nhật data integrated_partner theo dữ liệu bfsone_partner


### [R20250717]

1. Tasks:

- [Dan] - Cập nhật lại flowchart CRM, cập nhật hướng dẫn quotation., IB.
    Cập nhật hướng dẫn Dashboard.
    Cập nhật wiki về Inquiry Request, rule Status.
    Cập nhật tài liệu cho phần request pricing giá có sẵn.

- [An + Dan] - Enhance, fix bugs báo cáo Sale Performance team DB.

- [An] - Tạo Account, phân quyền các Công ty vệ tinh, VP nước ngoài.

- [Nhat] - Request Sis Sandy - Agent Transactions.
    - Report Agent Transactions theo dõi số lượng giao dịch của agent.

2. Migrate script:

- server:migrate:run --script crm/CreateCompany.groovy --company bee


### [R20250716]

1. [Nhat] - Cập nhật tính năng Clone Internal Booking.
          - Cập nhật Document bản English.

2. [Dan] - Báo cáo Key Account Performace (Doanh thu/ Profit khách hàng của từng sale).
  - Viết api để tự động sync dữ liệu house bill (bee legacy) cho saleman khi làm báo cáo.
    - Bee Legacy: Cập nhật api sync house bill theo date, saleman id.
    - DataTP: Cập nhật thêm nút để cho phép salesman refresh dữ liệu.

3. [Dan] - Request Mr. Tam (HCM) - Tổng hợp Daily Tasks của Saleman theo từng team.

### [R20250714]

1. [Dan] - Brainstorm, Design UI cho Inquiry Request, Quotation, Internal Booking.

- Giao diện theo kiểu tích hợp All-in-One tập trung tất cả thao tác (từ nhập yêu cầu, kiểm tra giá, tạo báo giá, đến gửi Internal Booking) trong một màn hình duy nhất

- Interactive Panel: Giao diện tương tác theo ngữ cảnh.
  - Ex: Render các action tương ứng khi chọn một inquiry request, quotation, internal booking.
  - Ex: Customize cho từng user (Cho phép ghim các báo giá của khách hàng quen thuộc, có hàng hàng ngày/ hàng tuần/ ...)

```
"Hệ quy chiếu của người báo giá là Quotation list, không phải Inquiry Request. Người báo giá sẽ theo dõi tất cả những báo giá của mình trên Quotation list.
- Mark win/fail/feedback... trên Quotation list & tự động link qua Inquiry Request list.
- Mark win trên Quotation list & tự động link qua IB list.
- Inquiry request list đánh giá hiệu quả công việc của pricing team. Có trường hợp salesman tạo inquiry request là để biết giá thị trường, không dùng giá đó để bán cho khách.
- Quotation list đánh giá hiệu quả công việc của team salesman."

```

2. [Nhat] Cập nhật query Pricing + Sales, loại các JOIN đến DB Core, migrate dữ liệu.
          Cập nhật tính năng archive/remove Asset Task.

2. Migrate script:
- migrate:run --script crm/AlterTables.groovy

### [R20250712]

1. Tasks:

  [Dan] - Fix bugs, enhance tạo, chỉnh sửa báo giá, internal booking.

  [An] - Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.
   Count số lần giá đó được saleman export.
   Sau khi tạo Internal Booking thành công -> Cập nhật feedback của bảng giá đó theo format:
     [Tên Saleman] - [Ngày Tạo Booking].
     [Incoterm]    - [Volume hàng].
     [Mô tả hàng]
   Viết cron để cập nhật ngày 2 lần.
   - 8AM: Tổng hợp Booking từ 14h ngày hôm qua đến trước 8h ngày hôm sau.
   - 14PM : Tổng hợp Booking từ 8h đến trước 14h ngày hiện tại.
   Từ Internal Booking -> Quotation -> QuotationCharge -> Reference Code.

2. Migrate script:

  - server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
  - server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
  - server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan
  - server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
  - server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
  - migrate:run --script crm/AlterTables.groovy

### [R20250710]

1. Tasks:
  - [Nhat] Cập nhật document Partners:
    + Key Account Performance Report.
    + Customer Leads: Check Exist Lead.
    + Customer Leads: New Agent Approach.
    + Customer Leads: Update Lead Star.
    + Tạo khách hàng, request to BFSOne.

  - [Nhat] Fix bugs tạo Request Xe/Phòng họp.


### [R20250708]

1. Tasks:
  - [Nhat] Cập nhật document Partners:
    + Key Account Performance Report.
    + Customer Leads: Check Exist Lead.
    + Customer Leads: New Agent Approach.
    + Customer Leads: Update Lead Star.
    + Tạo khách hàng, request to BFSOne.

  - [Nhat] Fix bugs tạo Request Xe/Phòng họp.

  - [An] - Thêm field variants Employees, cập nhật theo format để enhance search.
      - [Employee Name] - [Employee Code]
      - [Employee Name]

  - [Dan] - Fix bugs tạo quotation, internal booking.
  - [Dan] - Enhance search Employee, BBRefEmployee: thêm param cho phép filter employee toàn hệ thống.

  - [Dan] - Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.
    - Count số lần giá đó được saleman export gửi cho khách.
    - Sau khi tạo Internal Booking thành công -> Tự động cập nhật feedback cho bảng giá đó theo format:
        [Tên Saleman] - [Ngày Tạo Booking].
        [Incoterm]    - [Volume hàng].
        [Mô tả hàng]

2. Migrate script:
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company bee
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehph
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehan
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehcm
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beedad

  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company bee
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehph
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehan
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehcm
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beedad

### [R20250707]

1. Tasks:
  - [Dan] - Review Quotation - Booking process, clean code.
  - [Dan] - Tổ chức lại cấu trúc code, documentation.
  - [Nhat] - Tối ưu lại màn hình Lịch theo Tuần Book xe/phòng họp, bổ sung chức năng add email nhận thông báo.

2. Migrate script:
  - migrate:run --script crm/AlterTables.groovy

### [R20250702]

1. Tasks:
  - [Dan] - clean code datatp-logistics: module price/ sales/ management (module cũ đã tách thành datatp-crm)
  - [Dan] - Chỉnh báo cáo Key Account Performance cho teame BD - Mrs Hải y/c
  - [Dan] - Sửa lỗi Copy Quotation.
  - [Dan] - Báo cáo theo dõi sales, highlight các sales trực quan các sales không có activity.
  - [Dan] - Sync khách hàng cho tất cả employee bee corp/ beehcm/ bee dad.
  - [Dan] - Cập nhật, kiểm tra các script db, backup-tools.
      Document tại link https://gitlab.datatp.net/tuan/datatp-docs/-/blob/master/docs/shared/developer/SETUP.md

  - [Nhat] - CRM: Tracking số lần Saleman Export Quotation, hiển thị lên CRM Dashboard: Salesman Activity Tracker.
  - [Nhat] - Spreadsheet: Cập nhật màn hình Report team IST (BD HCM). Bổ sung thêm các biểu đồ báo cáo volume cho từng cá nhân:
    - TEU (1x20DC = 1 TEU, 1x40HC/1x45HC = 2 TEUs)
    - CBM (hàng LCL)
    - KG (hàng Air)
    - Shipment (Breakbulk).

  - [An] - Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c.
  - [An] - Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [An] - Fix bugs, enhance UI Bulk Cargo Inquiry Request - Mrs Cường BD yêu cầu.
  - [An] - Chỉnh form tạo mới Agent Approach.
  - [An] - Trên màn hình Dashboard, thêm các chức năng để cho phép view dữ liệu, mở rộng detail các chỉ số trên dashboard.

2. Migrate script:

### [R20250702]

1. Tasks:
  - [Nhat] Thêm 2 option cho button Feedback ở màn hình Sea FCL/LCL Price
      + Popup Form Request Price
      + Popup Form Feedback
  - [Nhat] Migrate dữ liệu forwarder_customer_leads: cập nhật company_id, saleman_label
        + script: migrate:run --script crm/AlterTables.groovy
  - [An] Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c
  - [An] Cập nhật theo feedback chỉnh sửa cho inquiry hàng rời
  - [An] Sync lại toàn bộ khách hàng cho sales HPH/ HAN
  - [An] Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [Dan] Enhance lọc, báo cáo volume theo tuyến cho team pricing.
  - [Dan] Enhance UI Sale Dashboard, UI Pricing Dashboard.

2. Migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehph
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehan

### [R20250701]
- Fix báo cáo Key Account Report.
- Enhance lại Pricing Dashboard, thêm báo cáo volume theo từng tuyến.
- Thêm báo cáo theo dõi khách hàng, chỉ số chuyển đổi khách hàng cho Sale Dashboard.
- Fix, sửa lỗi tạo Partner, api với BFSOne.

### [R20250626]
- [Nhat] - Fix bugs create partner, enhance màn hình CRM Sale Dashboard.
- [An] - Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH
         - Cố định row header và cột đầu tiên.
         - Kiểm tra lại chỉ số Khách hàng mới/ lead mới.

### [R20250621]

Cập nhật nhánh hr:
Task Cập nhật UI + Query Sync dữ liệu OKR tự động
- Bổ sung service type: Crossborder
- Bổ sung đếm số lượng tờ khai hải quan

1. [An] - Company Pricing Dashboard:
- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. [An] - Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.

### [R20250618]

[Dan] - Implement UI Dashboard Salesman Activity Tracker.

